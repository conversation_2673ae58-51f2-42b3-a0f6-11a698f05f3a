"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateConnectorRequest = void 0;
const core = __importStar(require("../../../../../core"));
const CreateConnectorOAuth_1 = require("../../../../types/CreateConnectorOAuth");
const CreateConnectorServiceAuth_1 = require("../../../../types/CreateConnectorServiceAuth");
exports.UpdateConnectorRequest = core.serialization.object({
    name: core.serialization.string().optional(),
    url: core.serialization.string().optional(),
    excludes: core.serialization.list(core.serialization.string()).optional(),
    oauth: CreateConnectorOAuth_1.CreateConnectorOAuth.optional(),
    active: core.serialization.boolean().optional(),
    continueOnFailure: core.serialization.property("continue_on_failure", core.serialization.boolean().optional()),
    serviceAuth: core.serialization.property("service_auth", CreateConnectorServiceAuth_1.CreateConnectorServiceAuth.optional()),
});
