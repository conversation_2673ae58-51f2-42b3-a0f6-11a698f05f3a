"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;
const SRANDMEMBER_1 = require("./SRANDMEMBER");
var SRANDMEMBER_2 = require("./SRANDMEMBER");
Object.defineProperty(exports, "FIRST_KEY_INDEX", { enumerable: true, get: function () { return SRANDMEMBER_2.FIRST_KEY_INDEX; } });
function transformArguments(key, count) {
    return [
        ...(0, SRANDMEMBER_1.transformArguments)(key),
        count.toString()
    ];
}
exports.transformArguments = transformArguments;
