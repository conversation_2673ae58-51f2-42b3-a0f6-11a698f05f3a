{"name": "fitpersona-api", "version": "1.0.0", "description": "🏋️‍♂️ FitPersona REST API - Kişiye Özel Spor ve BMI Asistanı", "main": "api_server.js", "scripts": {"start": "node api_server.js", "dev": "nodemon api_server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["fitness", "bmi", "health", "exercise", "nutrition", "api", "react-native"], "author": "FitPersona Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}