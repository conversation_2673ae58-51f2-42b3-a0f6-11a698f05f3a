{"version": 3, "sources": ["ScaleGestureDetector.ts"], "names": ["DEFAULT_TOUCH_SLOP", "EventTypes", "ScaleGestureDetector", "constructor", "callbacks", "onScaleBegin", "onScale", "onScaleEnd", "spanSlop", "minSpan", "onTouchEvent", "event", "tracker", "currentTime", "time", "action", "eventType", "numOfPointers", "trackedPointersCount", "streamComplete", "UP", "ADDITIONAL_POINTER_UP", "CANCEL", "DOWN", "inProgress", "initialSpan", "config<PERSON><PERSON><PERSON>", "ADDITIONAL_POINTER_DOWN", "pointerUp", "ignoredPointer", "pointerId", "undefined", "div", "coordsSum", "getAbsoluteCoordsSum", "focusX", "x", "focusY", "y", "devSumX", "devSumY", "trackedPointers", "for<PERSON>ach", "value", "key", "Math", "abs", "abosoluteCoords", "devX", "devY", "spanX", "spanY", "span", "hypot", "wasInProgress", "_focusX", "_focusY", "prevSpan", "_currentSpan", "prevTime", "MOVE", "currentSpan", "calculateScaleFactor", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;AAAA,SAASA,kBAAT,QAAmC,cAAnC;AACA,SAAuBC,UAAvB,QAAyC,eAAzC;AAUA,eAAe,MAAMC,oBAAN,CAA2D;AAoBxEC,EAAAA,WAAW,CAACC,SAAD,EAAkC;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,wCALxB,KAKwB;;AAAA;;AAAA;;AAC3C,SAAKC,YAAL,GAAoBD,SAAS,CAACC,YAA9B;AACA,SAAKC,OAAL,GAAeF,SAAS,CAACE,OAAzB;AACA,SAAKC,UAAL,GAAkBH,SAAS,CAACG,UAA5B;AAEA,SAAKC,QAAL,GAAgBR,kBAAkB,GAAG,CAArC;AACA,SAAKS,OAAL,GAAe,CAAf;AACD;;AAEMC,EAAAA,YAAY,CAACC,KAAD,EAAsBC,OAAtB,EAAwD;AACzE,SAAKC,WAAL,GAAmBF,KAAK,CAACG,IAAzB;AAEA,UAAMC,MAAkB,GAAGJ,KAAK,CAACK,SAAjC;AACA,UAAMC,aAAa,GAAGL,OAAO,CAACM,oBAA9B;AAEA,UAAMC,cAAuB,GAC3BJ,MAAM,KAAKd,UAAU,CAACmB,EAAtB,IACAL,MAAM,KAAKd,UAAU,CAACoB,qBADtB,IAEAN,MAAM,KAAKd,UAAU,CAACqB,MAHxB;;AAKA,QAAIP,MAAM,KAAKd,UAAU,CAACsB,IAAtB,IAA8BJ,cAAlC,EAAkD;AAChD,UAAI,KAAKK,UAAT,EAAqB;AACnB,aAAKjB,UAAL,CAAgB,IAAhB;AACA,aAAKiB,UAAL,GAAkB,KAAlB;AACA,aAAKC,WAAL,GAAmB,CAAnB;AACD;;AAED,UAAIN,cAAJ,EAAoB;AAClB,eAAO,IAAP;AACD;AACF;;AAED,UAAMO,aAAsB,GAC1BX,MAAM,KAAKd,UAAU,CAACsB,IAAtB,IACAR,MAAM,KAAKd,UAAU,CAACoB,qBADtB,IAEAN,MAAM,KAAKd,UAAU,CAAC0B,uBAHxB;AAKA,UAAMC,SAAS,GAAGb,MAAM,KAAKd,UAAU,CAACoB,qBAAxC;AAEA,UAAMQ,cAAkC,GAAGD,SAAS,GAChDjB,KAAK,CAACmB,SAD0C,GAEhDC,SAFJ,CA9ByE,CAkCzE;;AAEA,UAAMC,GAAW,GAAGJ,SAAS,GAAGX,aAAa,GAAG,CAAnB,GAAuBA,aAApD;AAEA,UAAMgB,SAAS,GAAGrB,OAAO,CAACsB,oBAAR,EAAlB;AAEA,UAAMC,MAAM,GAAGF,SAAS,CAACG,CAAV,GAAcJ,GAA7B;AACA,UAAMK,MAAM,GAAGJ,SAAS,CAACK,CAAV,GAAcN,GAA7B,CAzCyE,CA2CzE;;AAEA,QAAIO,OAAO,GAAG,CAAd;AACA,QAAIC,OAAO,GAAG,CAAd;AAEA5B,IAAAA,OAAO,CAAC6B,eAAR,CAAwBC,OAAxB,CAAgC,CAACC,KAAD,EAAQC,GAAR,KAAgB;AAC9C,UAAIA,GAAG,KAAKf,cAAZ,EAA4B;AAC1B;AACD;;AAEDU,MAAAA,OAAO,IAAIM,IAAI,CAACC,GAAL,CAASH,KAAK,CAACI,eAAN,CAAsBX,CAAtB,GAA0BD,MAAnC,CAAX;AACAK,MAAAA,OAAO,IAAIK,IAAI,CAACC,GAAL,CAASH,KAAK,CAACI,eAAN,CAAsBT,CAAtB,GAA0BD,MAAnC,CAAX;AACD,KAPD;AASA,UAAMW,IAAY,GAAGT,OAAO,GAAGP,GAA/B;AACA,UAAMiB,IAAY,GAAGT,OAAO,GAAGR,GAA/B;AAEA,UAAMkB,KAAa,GAAGF,IAAI,GAAG,CAA7B;AACA,UAAMG,KAAa,GAAGF,IAAI,GAAG,CAA7B;AAEA,UAAMG,IAAI,GAAGP,IAAI,CAACQ,KAAL,CAAWH,KAAX,EAAkBC,KAAlB,CAAb,CA/DyE,CAiEzE;;AACA,UAAMG,aAAsB,GAAG,KAAK9B,UAApC;AACA,SAAK+B,OAAL,GAAepB,MAAf;AACA,SAAKqB,OAAL,GAAenB,MAAf;;AAEA,QAAI,KAAKb,UAAL,KAAoB4B,IAAI,GAAG,KAAK3C,OAAZ,IAAuBiB,aAA3C,CAAJ,EAA+D;AAC7D,WAAKnB,UAAL,CAAgB,IAAhB;AACA,WAAKiB,UAAL,GAAkB,KAAlB;AACA,WAAKC,WAAL,GAAmB2B,IAAnB;AACD;;AAED,QAAI1B,aAAJ,EAAmB;AACjB,WAAKD,WAAL,GAAmB,KAAKgC,QAAL,GAAgB,KAAKC,YAAL,GAAoBN,IAAvD;AACD;;AAED,QACE,CAAC,KAAK5B,UAAN,IACA4B,IAAI,IAAI,KAAK3C,OADb,KAEC6C,aAAa,IAAIT,IAAI,CAACC,GAAL,CAASM,IAAI,GAAG,KAAK3B,WAArB,IAAoC,KAAKjB,QAF3D,CADF,EAIE;AACA,WAAKiD,QAAL,GAAgB,KAAKC,YAAL,GAAoBN,IAApC;AACA,WAAKO,QAAL,GAAgB,KAAK9C,WAArB;AACA,WAAKW,UAAL,GAAkB,KAAKnB,YAAL,CAAkB,IAAlB,CAAlB;AACD,KAxFwE,CA0FzE;;;AACA,QAAIU,MAAM,KAAKd,UAAU,CAAC2D,IAA1B,EAAgC;AAC9B,aAAO,IAAP;AACD;;AAED,SAAKF,YAAL,GAAoBN,IAApB;;AAEA,QAAI,KAAK5B,UAAL,IAAmB,CAAC,KAAKlB,OAAL,CAAa,IAAb,CAAxB,EAA4C;AAC1C,aAAO,IAAP;AACD;;AAED,SAAKmD,QAAL,GAAgB,KAAKI,WAArB;AACA,SAAKF,QAAL,GAAgB,KAAK9C,WAArB;AAEA,WAAO,IAAP;AACD;;AAEMiD,EAAAA,oBAAoB,CAAC7C,aAAD,EAAgC;AACzD,QAAIA,aAAa,GAAG,CAApB,EAAuB;AACrB,aAAO,CAAP;AACD;;AAED,WAAO,KAAKwC,QAAL,GAAgB,CAAhB,GAAoB,KAAKI,WAAL,GAAmB,KAAKJ,QAA5C,GAAuD,CAA9D;AACD;;AAEqB,MAAXI,WAAW,GAAG;AACvB,WAAO,KAAKH,YAAZ;AACD;;AAEgB,MAANvB,MAAM,GAAG;AAClB,WAAO,KAAKoB,OAAZ;AACD;;AAEgB,MAANlB,MAAM,GAAG;AAClB,WAAO,KAAKmB,OAAZ;AACD;;AAEmB,MAATO,SAAS,GAAG;AACrB,WAAO,KAAKlD,WAAL,GAAmB,KAAK8C,QAA/B;AACD;;AA9JuE", "sourcesContent": ["import { DEFAULT_TOUCH_SLOP } from '../constants';\nimport { AdaptedEvent, EventTypes } from '../interfaces';\n\nimport PointerTracker from '../tools/PointerTracker';\n\nexport interface ScaleGestureListener {\n  onScaleBegin: (detector: ScaleGestureDetector) => boolean;\n  onScale: (detector: ScaleGestureDetector) => boolean;\n  onScaleEnd: (detector: ScaleGestureDetector) => void;\n}\n\nexport default class ScaleGestureDetector implements ScaleGestureListener {\n  public onScaleBegin: (detector: ScaleGestureDetector) => boolean;\n  public onScale: (detector: ScaleGestureDetector) => boolean;\n  public onScaleEnd: (detector: ScaleGestureDetector) => void;\n\n  private _focusX!: number;\n  private _focusY!: number;\n\n  private _currentSpan!: number;\n  private prevSpan!: number;\n  private initialSpan!: number;\n\n  private currentTime!: number;\n  private prevTime!: number;\n\n  private inProgress = false;\n\n  private spanSlop: number;\n  private minSpan: number;\n\n  constructor(callbacks: ScaleGestureListener) {\n    this.onScaleBegin = callbacks.onScaleBegin;\n    this.onScale = callbacks.onScale;\n    this.onScaleEnd = callbacks.onScaleEnd;\n\n    this.spanSlop = DEFAULT_TOUCH_SLOP * 2;\n    this.minSpan = 0;\n  }\n\n  public onTouchEvent(event: AdaptedEvent, tracker: PointerTracker): boolean {\n    this.currentTime = event.time;\n\n    const action: EventTypes = event.eventType;\n    const numOfPointers = tracker.trackedPointersCount;\n\n    const streamComplete: boolean =\n      action === EventTypes.UP ||\n      action === EventTypes.ADDITIONAL_POINTER_UP ||\n      action === EventTypes.CANCEL;\n\n    if (action === EventTypes.DOWN || streamComplete) {\n      if (this.inProgress) {\n        this.onScaleEnd(this);\n        this.inProgress = false;\n        this.initialSpan = 0;\n      }\n\n      if (streamComplete) {\n        return true;\n      }\n    }\n\n    const configChanged: boolean =\n      action === EventTypes.DOWN ||\n      action === EventTypes.ADDITIONAL_POINTER_UP ||\n      action === EventTypes.ADDITIONAL_POINTER_DOWN;\n\n    const pointerUp = action === EventTypes.ADDITIONAL_POINTER_UP;\n\n    const ignoredPointer: number | undefined = pointerUp\n      ? event.pointerId\n      : undefined;\n\n    // Determine focal point\n\n    const div: number = pointerUp ? numOfPointers - 1 : numOfPointers;\n\n    const coordsSum = tracker.getAbsoluteCoordsSum();\n\n    const focusX = coordsSum.x / div;\n    const focusY = coordsSum.y / div;\n\n    // Determine average deviation from focal point\n\n    let devSumX = 0;\n    let devSumY = 0;\n\n    tracker.trackedPointers.forEach((value, key) => {\n      if (key === ignoredPointer) {\n        return;\n      }\n\n      devSumX += Math.abs(value.abosoluteCoords.x - focusX);\n      devSumY += Math.abs(value.abosoluteCoords.y - focusY);\n    });\n\n    const devX: number = devSumX / div;\n    const devY: number = devSumY / div;\n\n    const spanX: number = devX * 2;\n    const spanY: number = devY * 2;\n\n    const span = Math.hypot(spanX, spanY);\n\n    // Begin/end events\n    const wasInProgress: boolean = this.inProgress;\n    this._focusX = focusX;\n    this._focusY = focusY;\n\n    if (this.inProgress && (span < this.minSpan || configChanged)) {\n      this.onScaleEnd(this);\n      this.inProgress = false;\n      this.initialSpan = span;\n    }\n\n    if (configChanged) {\n      this.initialSpan = this.prevSpan = this._currentSpan = span;\n    }\n\n    if (\n      !this.inProgress &&\n      span >= this.minSpan &&\n      (wasInProgress || Math.abs(span - this.initialSpan) > this.spanSlop)\n    ) {\n      this.prevSpan = this._currentSpan = span;\n      this.prevTime = this.currentTime;\n      this.inProgress = this.onScaleBegin(this);\n    }\n\n    // Handle motion\n    if (action !== EventTypes.MOVE) {\n      return true;\n    }\n\n    this._currentSpan = span;\n\n    if (this.inProgress && !this.onScale(this)) {\n      return true;\n    }\n\n    this.prevSpan = this.currentSpan;\n    this.prevTime = this.currentTime;\n\n    return true;\n  }\n\n  public calculateScaleFactor(numOfPointers: number): number {\n    if (numOfPointers < 2) {\n      return 1;\n    }\n\n    return this.prevSpan > 0 ? this.currentSpan / this.prevSpan : 1;\n  }\n\n  public get currentSpan() {\n    return this._currentSpan;\n  }\n\n  public get focusX() {\n    return this._focusX;\n  }\n\n  public get focusY() {\n    return this._focusY;\n  }\n\n  public get timeDelta() {\n    return this.currentTime - this.prevTime;\n  }\n}\n"]}