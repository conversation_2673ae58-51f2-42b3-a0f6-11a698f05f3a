
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeCpp.js
 */

#include <react/renderer/components/rnscreens/ShadowNodes.h>

namespace facebook::react {

extern const char RNSScreenContainerComponentName[] = "RNSScreenContainer";
extern const char RNSScreenContentWrapperComponentName[] = "RNSScreenContentWrapper";
extern const char RNSScreenFooterComponentName[] = "RNSScreenFooter";
extern const char RNSScreenNavigationContainerComponentName[] = "RNSScreenNavigationContainer";
extern const char RNSScreenStackComponentName[] = "RNSScreenStack";
extern const char RNSSearchBarComponentName[] = "RNSSearchBar";

} // namespace facebook::react
