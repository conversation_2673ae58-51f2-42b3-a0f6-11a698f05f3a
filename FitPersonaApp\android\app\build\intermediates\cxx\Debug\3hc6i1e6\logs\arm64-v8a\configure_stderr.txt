CMake Warning in C:/Users/<USER>/Desktop/Weather-Forecast-MCP-main/FitPersonaApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Desktop/Weather-Forecast-MCP-main/FitPersonaApp/android/app/.cxx/Debug/3hc6i1e6/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/./

  has 200 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in C:/Users/<USER>/Desktop/Weather-Forecast-MCP-main/FitPersonaApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Desktop/Weather-Forecast-MCP-main/FitPersonaApp/android/app/.cxx/Debug/3hc6i1e6/arm64-v8a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/./

  has 186 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


