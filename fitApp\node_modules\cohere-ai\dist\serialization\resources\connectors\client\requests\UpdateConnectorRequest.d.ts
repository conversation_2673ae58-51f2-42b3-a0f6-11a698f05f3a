/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../index";
import * as Cohere from "../../../../../api/index";
import * as core from "../../../../../core";
import { CreateConnectorOAuth } from "../../../../types/CreateConnectorOAuth";
import { CreateConnectorServiceAuth } from "../../../../types/CreateConnectorServiceAuth";
export declare const UpdateConnectorRequest: core.serialization.Schema<serializers.UpdateConnectorRequest.Raw, Cohere.UpdateConnectorRequest>;
export declare namespace UpdateConnectorRequest {
    interface Raw {
        name?: string | null;
        url?: string | null;
        excludes?: string[] | null;
        oauth?: CreateConnectorOAuth.Raw | null;
        active?: boolean | null;
        continue_on_failure?: boolean | null;
        service_auth?: CreateConnectorServiceAuth.Raw | null;
    }
}
