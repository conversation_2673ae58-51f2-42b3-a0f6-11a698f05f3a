{"version": 3, "sources": ["Pressable.tsx"], "names": ["React", "forwardRef", "useCallback", "useMemo", "useRef", "useState", "GestureObjects", "Gesture", "GestureDetector", "Platform", "processColor", "NativeButton", "numberAsInset", "gestureToPressableEvent", "isTouchWithinInset", "gestureTouchToPressableEvent", "addInsets", "PressabilityDebugView", "INT32_MAX", "isF<PERSON><PERSON>", "isTestEnv", "DEFAULT_LONG_PRESS_DURATION", "IS_TEST_ENV", "IS_FABRIC", "Pressable", "props", "pressableRef", "testOnly_pressed", "hitSlop", "pressRetentionOffset", "delayHoverIn", "onHoverIn", "delayHoverOut", "onHoverOut", "delayLongPress", "unstable_pressDelay", "onPress", "onPressIn", "onPressOut", "onLongPress", "style", "children", "android_disableSound", "android_ripple", "disabled", "accessible", "remainingProps", "pressedState", "setPressedState", "isPressCallbackEnabled", "hasPassedBoundsChecks", "shouldPreventNativeEffects", "normalizedHitSlop", "normalizedPressRetentionOffset", "hoverInTimeout", "hoverOutTimeout", "hoverGesture", "Hover", "manualActivation", "cancelsTouchesInView", "onBegin", "event", "current", "clearTimeout", "setTimeout", "onFinalize", "pressDelayTimeoutRef", "isTouchPropagationAllowed", "deferredEventPayload", "pressInHandler", "handlingOnTouchesDown", "pressOutHandler", "longPressTimeoutRef", "nativeEvent", "touches", "length", "changedTouches", "onEndHandlingTouchesDown", "cancelledMidPress", "activateLongPress", "longPressMinDuration", "innerPressableRef", "measureCallback", "width", "height", "at", "pressAndTouchGesture", "Long<PERSON>ress", "minDuration", "maxDistance", "onTouchesDown", "measure", "_x", "_y", "onTouchesUp", "onTouchesCancelled", "allTouches", "buttonGesture", "Native", "OS", "onStart", "appliedHitSlop", "isPressableEnabled", "gestures", "gesture", "enabled", "runOnJS", "shouldCancelWhenOutside", "Simultaneous", "pointerStyle", "cursor", "styleProp", "pressed", "childrenProp", "rippleColor", "defaultRippleColor", "undefined", "unprocessedRippleColor", "color", "radius", "__DEV__"], "mappings": ";;AAAA,OAAOA,KAAP,IAEEC,UAFF,EAIEC,WAJF,EAKEC,OALF,EAMEC,MANF,EAOEC,QAPF,QAQO,OARP;AASA,SAASC,cAAc,IAAIC,OAA3B,QAA0C,wCAA1C;AACA,SAASC,eAAT,QAAgC,yCAAhC;AAEA,SAEEC,QAFF,EAMEC,YANF,QAOO,cAPP;AAQA,OAAOC,YAAP,MAAyB,yBAAzB;AACA,SACEC,aADF,EAEEC,uBAFF,EAGEC,kBAHF,EAIEC,4BAJF,EAKEC,SALF,QAMO,SANP;AAOA,SAASC,qBAAT,QAAsC,sCAAtC;AAEA,SAASC,SAAT,EAAoBC,QAApB,EAA8BC,SAA9B,QAA+C,aAA/C;AAEA,MAAMC,2BAA2B,GAAG,GAApC;AACA,MAAMC,WAAW,GAAGF,SAAS,EAA7B;AAEA,IAAIG,SAAyB,GAAG,IAAhC;AAEA,MAAMC,SAAS,gBAAGvB,UAAU,CAC1B,CAACwB,KAAD,EAAwBC,YAAxB,KAA6D;AAAA;;AAC3D,QAAM;AACJC,IAAAA,gBADI;AAEJC,IAAAA,OAFI;AAGJC,IAAAA,oBAHI;AAIJC,IAAAA,YAJI;AAKJC,IAAAA,SALI;AAMJC,IAAAA,aANI;AAOJC,IAAAA,UAPI;AAQJC,IAAAA,cARI;AASJC,IAAAA,mBATI;AAUJC,IAAAA,OAVI;AAWJC,IAAAA,SAXI;AAYJC,IAAAA,UAZI;AAaJC,IAAAA,WAbI;AAcJC,IAAAA,KAdI;AAeJC,IAAAA,QAfI;AAgBJC,IAAAA,oBAhBI;AAiBJC,IAAAA,cAjBI;AAkBJC,IAAAA,QAlBI;AAmBJC,IAAAA,UAnBI;AAoBJ,OAAGC;AApBC,MAqBFrB,KArBJ;AAuBA,QAAM,CAACsB,YAAD,EAAeC,eAAf,IAAkC3C,QAAQ,CAACsB,gBAAD,aAACA,gBAAD,cAACA,gBAAD,GAAqB,KAArB,CAAhD,CAxB2D,CA0B3D;;AACA,QAAMsB,sBAAsB,GAAG7C,MAAM,CAAU,IAAV,CAArC;AACA,QAAM8C,qBAAqB,GAAG9C,MAAM,CAAU,KAAV,CAApC;AACA,QAAM+C,0BAA0B,GAAG/C,MAAM,CAAU,KAAV,CAAzC;AAEA,QAAMgD,iBAAyB,GAAGjD,OAAO,CACvC,MACE,OAAOyB,OAAP,KAAmB,QAAnB,GAA8BhB,aAAa,CAACgB,OAAD,CAA3C,GAAwDA,OAAxD,aAAwDA,OAAxD,cAAwDA,OAAxD,GAAmE,EAF9B,EAGvC,CAACA,OAAD,CAHuC,CAAzC;AAMA,QAAMyB,8BAAsC,GAAGlD,OAAO,CACpD,MACE,OAAO0B,oBAAP,KAAgC,QAAhC,GACIjB,aAAa,CAACiB,oBAAD,CADjB,GAEKA,oBAFL,aAEKA,oBAFL,cAEKA,oBAFL,GAE6B,EAJqB,EAKpD,CAACA,oBAAD,CALoD,CAAtD;AAQA,QAAMyB,cAAc,GAAGlD,MAAM,CAAgB,IAAhB,CAA7B;AACA,QAAMmD,eAAe,GAAGnD,MAAM,CAAgB,IAAhB,CAA9B;AAEA,QAAMoD,YAAY,GAAGrD,OAAO,CAC1B,MACEI,OAAO,CAACkD,KAAR,GACGC,gBADH,CACoB,IADpB,EAC0B;AAD1B,GAEGC,oBAFH,CAEwB,KAFxB,EAGGC,OAHH,CAGYC,KAAD,IAAW;AAClB,QAAIN,eAAe,CAACO,OAApB,EAA6B;AAC3BC,MAAAA,YAAY,CAACR,eAAe,CAACO,OAAjB,CAAZ;AACD;;AACD,QAAIhC,YAAJ,EAAkB;AAChBwB,MAAAA,cAAc,CAACQ,OAAf,GAAyBE,UAAU,CACjC,MAAMjC,SAAN,aAAMA,SAAN,uBAAMA,SAAS,CAAGlB,uBAAuB,CAACgD,KAAD,CAA1B,CADkB,EAEjC/B,YAFiC,CAAnC;AAIA;AACD;;AACDC,IAAAA,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAGlB,uBAAuB,CAACgD,KAAD,CAA1B,CAAT;AACD,GAfH,EAgBGI,UAhBH,CAgBeJ,KAAD,IAAW;AACrB,QAAIP,cAAc,CAACQ,OAAnB,EAA4B;AAC1BC,MAAAA,YAAY,CAACT,cAAc,CAACQ,OAAhB,CAAZ;AACD;;AACD,QAAI9B,aAAJ,EAAmB;AACjBuB,MAAAA,eAAe,CAACO,OAAhB,GAA0BE,UAAU,CAClC,MAAM/B,UAAN,aAAMA,UAAN,uBAAMA,UAAU,CAAGpB,uBAAuB,CAACgD,KAAD,CAA1B,CADkB,EAElC7B,aAFkC,CAApC;AAIA;AACD;;AACDC,IAAAA,UAAU,SAAV,IAAAA,UAAU,WAAV,YAAAA,UAAU,CAAGpB,uBAAuB,CAACgD,KAAD,CAA1B,CAAV;AACD,GA5BH,CAFwB,EA+B1B,CAAC/B,YAAD,EAAeE,aAAf,EAA8BD,SAA9B,EAAyCE,UAAzC,CA/B0B,CAA5B;AAkCA,QAAMiC,oBAAoB,GAAG9D,MAAM,CAAgB,IAAhB,CAAnC;AACA,QAAM+D,yBAAyB,GAAG/D,MAAM,CAAU,KAAV,CAAxC,CAnF2D,CAqF3D;;AACA,QAAMgE,oBAAoB,GAAGhE,MAAM,CAAwB,IAAxB,CAAnC;AAEA,QAAMiE,cAAc,GAAGnE,WAAW,CAC/B2D,KAAD,IAA2B;AACzB,QAAIS,qBAAqB,CAACR,OAA1B,EAAmC;AACjCM,MAAAA,oBAAoB,CAACN,OAArB,GAA+BD,KAA/B;AACD;;AAED,QAAI,CAACM,yBAAyB,CAACL,OAA/B,EAAwC;AACtC;AACD;;AAEDM,IAAAA,oBAAoB,CAACN,OAArB,GAA+B,IAA/B;AAEAzB,IAAAA,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAGwB,KAAH,CAAT;AACAZ,IAAAA,sBAAsB,CAACa,OAAvB,GAAiC,IAAjC;AACAI,IAAAA,oBAAoB,CAACJ,OAArB,GAA+B,IAA/B;AACAd,IAAAA,eAAe,CAAC,IAAD,CAAf;AACD,GAhB+B,EAiBhC,CAACX,SAAD,CAjBgC,CAAlC;AAoBA,QAAMkC,eAAe,GAAGrE,WAAW,CAChC2D,KAAD,IAA2B;AACzB,QAAI,CAACM,yBAAyB,CAACL,OAA/B,EAAwC;AACtCZ,MAAAA,qBAAqB,CAACY,OAAtB,GAAgC,KAAhC;AACAb,MAAAA,sBAAsB,CAACa,OAAvB,GAAiC,IAAjC;AACAM,MAAAA,oBAAoB,CAACN,OAArB,GAA+B,IAA/B;;AAEA,UAAIU,mBAAmB,CAACV,OAAxB,EAAiC;AAC/BC,QAAAA,YAAY,CAACS,mBAAmB,CAACV,OAArB,CAAZ;AACAU,QAAAA,mBAAmB,CAACV,OAApB,GAA8B,IAA9B;AACD;;AAED,UAAII,oBAAoB,CAACJ,OAAzB,EAAkC;AAChCC,QAAAA,YAAY,CAACG,oBAAoB,CAACJ,OAAtB,CAAZ;AACAI,QAAAA,oBAAoB,CAACJ,OAArB,GAA+B,IAA/B;AACD;;AAED;AACD;;AAED,QACE,CAACZ,qBAAqB,CAACY,OAAvB,IACAD,KAAK,CAACY,WAAN,CAAkBC,OAAlB,CAA0BC,MAA1B,GACEd,KAAK,CAACY,WAAN,CAAkBG,cAAlB,CAAiCD,MAHrC,EAIE;AACA;AACD;;AAED,QAAIxC,mBAAmB,IAAI+B,oBAAoB,CAACJ,OAArB,KAAiC,IAA5D,EAAkE;AAChE;AACA;AACA;AACAC,MAAAA,YAAY,CAACG,oBAAoB,CAACJ,OAAtB,CAAZ;AACAO,MAAAA,cAAc,CAACR,KAAD,CAAd;AACD;;AAED,QAAIO,oBAAoB,CAACN,OAAzB,EAAkC;AAChCzB,MAAAA,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAG+B,oBAAoB,CAACN,OAAxB,CAAT;AACAM,MAAAA,oBAAoB,CAACN,OAArB,GAA+B,IAA/B;AACD;;AAEDxB,IAAAA,UAAU,SAAV,IAAAA,UAAU,WAAV,YAAAA,UAAU,CAAGuB,KAAH,CAAV;;AAEA,QAAIZ,sBAAsB,CAACa,OAA3B,EAAoC;AAClC1B,MAAAA,OAAO,SAAP,IAAAA,OAAO,WAAP,YAAAA,OAAO,CAAGyB,KAAH,CAAP;AACD;;AAED,QAAIW,mBAAmB,CAACV,OAAxB,EAAiC;AAC/BC,MAAAA,YAAY,CAACS,mBAAmB,CAACV,OAArB,CAAZ;AACAU,MAAAA,mBAAmB,CAACV,OAApB,GAA8B,IAA9B;AACD;;AAEDK,IAAAA,yBAAyB,CAACL,OAA1B,GAAoC,KAApC;AACAZ,IAAAA,qBAAqB,CAACY,OAAtB,GAAgC,KAAhC;AACAb,IAAAA,sBAAsB,CAACa,OAAvB,GAAiC,IAAjC;AACAd,IAAAA,eAAe,CAAC,KAAD,CAAf;AACD,GAxDgC,EAyDjC,CAACZ,OAAD,EAAUC,SAAV,EAAqBC,UAArB,EAAiC+B,cAAjC,EAAiDlC,mBAAjD,CAzDiC,CAAnC;AA4DA,QAAMmC,qBAAqB,GAAGlE,MAAM,CAAU,KAAV,CAApC;AACA,QAAMyE,wBAAwB,GAAGzE,MAAM,CAAsB,IAAtB,CAAvC;AACA,QAAM0E,iBAAiB,GAAG1E,MAAM,CAAU,KAAV,CAAhC;AAEA,QAAM2E,iBAAiB,GAAG7E,WAAW,CAClC2D,KAAD,IAA8B;AAC5B,QAAI,CAACM,yBAAyB,CAACL,OAA/B,EAAwC;AACtC;AACD;;AAED,QAAIZ,qBAAqB,CAACY,OAA1B,EAAmC;AACjCvB,MAAAA,WAAW,SAAX,IAAAA,WAAW,WAAX,YAAAA,WAAW,CAAGxB,4BAA4B,CAAC8C,KAAD,CAA/B,CAAX;AACAZ,MAAAA,sBAAsB,CAACa,OAAvB,GAAiC,KAAjC;AACD;;AAED,QAAIU,mBAAmB,CAACV,OAAxB,EAAiC;AAC/BC,MAAAA,YAAY,CAACS,mBAAmB,CAACV,OAArB,CAAZ;AACAU,MAAAA,mBAAmB,CAACV,OAApB,GAA8B,IAA9B;AACD;AACF,GAfkC,EAgBnC,CAACvB,WAAD,CAhBmC,CAArC;AAmBA,QAAMiC,mBAAmB,GAAGpE,MAAM,CAAgB,IAAhB,CAAlC;AACA,QAAM4E,oBAAoB,GACxB,CAAC9C,cAAD,aAACA,cAAD,cAACA,cAAD,GAAmBb,2BAAnB,KACCc,mBADD,aACCA,mBADD,cACCA,mBADD,GACwB,CADxB,CADF;AAIA,QAAM8C,iBAAiB,GAAG7E,MAAM,CAAO,IAAP,CAAhC;AAEA,QAAM8E,eAAe,GAAGhF,WAAW,CACjC,CAACiF,KAAD,EAAgBC,MAAhB,EAAgCvB,KAAhC,KAA6D;AAAA;;AAC3D,QACE,CAAC/C,kBAAkB,CACjB;AACEqE,MAAAA,KADF;AAEEC,MAAAA;AAFF,KADiB,EAKjBhC,iBALiB,EAMjBS,KAAK,CAACe,cAAN,CAAqBS,EAArB,CAAwB,CAAC,CAAzB,CANiB,CAAnB,IAQAnC,qBAAqB,CAACY,OARtB,IASAgB,iBAAiB,CAAChB,OAVpB,EAWE;AACAgB,MAAAA,iBAAiB,CAAChB,OAAlB,GAA4B,KAA5B;AACAe,MAAAA,wBAAwB,CAACf,OAAzB,GAAmC,IAAnC;AACAQ,MAAAA,qBAAqB,CAACR,OAAtB,GAAgC,KAAhC;AACA;AACD;;AAEDZ,IAAAA,qBAAqB,CAACY,OAAtB,GAAgC,IAAhC,CAnB2D,CAqB3D;;AACA,QAAIU,mBAAmB,CAACV,OAApB,KAAgC,IAApC,EAA0C;AACxC;AACAU,MAAAA,mBAAmB,CAACV,OAApB,GAA8BE,UAAU,CACtC,MAAMe,iBAAiB,CAAClB,KAAD,CADe,EAEtCmB,oBAFsC,CAAxC;AAID;;AAED,QAAI7C,mBAAJ,EAAyB;AACvB+B,MAAAA,oBAAoB,CAACJ,OAArB,GAA+BE,UAAU,CAAC,MAAM;AAC9CK,QAAAA,cAAc,CAACtD,4BAA4B,CAAC8C,KAAD,CAA7B,CAAd;AACD,OAFwC,EAEtC1B,mBAFsC,CAAzC;AAGD,KAJD,MAIO;AACLkC,MAAAA,cAAc,CAACtD,4BAA4B,CAAC8C,KAAD,CAA7B,CAAd;AACD;;AAED,6BAAAgB,wBAAwB,CAACf,OAAzB,qFAAAe,wBAAwB;AACxBA,IAAAA,wBAAwB,CAACf,OAAzB,GAAmC,IAAnC;AACAQ,IAAAA,qBAAqB,CAACR,OAAtB,GAAgC,KAAhC;AACD,GA1CgC,EA2CjC,CACEiB,iBADF,EAEEC,oBAFF,EAGE5B,iBAHF,EAIEiB,cAJF,EAKElC,mBALF,CA3CiC,CAAnC;AAoDA,QAAMmD,oBAAoB,GAAGnF,OAAO,CAClC,MACEI,OAAO,CAACgF,SAAR,GACGC,WADH,CACetE,SADf,EAC0B;AAD1B,GAEGuE,WAFH,CAEevE,SAFf,EAE0B;AAF1B,GAGGyC,oBAHH,CAGwB,KAHxB,EAIG+B,aAJH,CAIkB7B,KAAD,IAAW;AACxBS,IAAAA,qBAAqB,CAACR,OAAtB,GAAgC,IAAhC;;AACA,QAAIpC,YAAJ,EAAkB;AAAA;;AAChB,kBAACA,YAAD,CAAkCoC,OAAlC,sDAA2C6B,OAA3C,CACE,CAACC,EAAD,EAAKC,EAAL,EAASV,KAAT,EAAgBC,MAAhB,KAA2B;AACzBF,QAAAA,eAAe,CAACC,KAAD,EAAQC,MAAR,EAAgBvB,KAAhB,CAAf;AACD,OAHH;AAKD,KAND,MAMO;AAAA;;AACL,+BAAAoB,iBAAiB,CAACnB,OAAlB,gFAA2B6B,OAA3B,CAAmC,CAACC,EAAD,EAAKC,EAAL,EAASV,KAAT,EAAgBC,MAAhB,KAA2B;AAC5DF,QAAAA,eAAe,CAACC,KAAD,EAAQC,MAAR,EAAgBvB,KAAhB,CAAf;AACD,OAFD;AAGD;AACF,GAjBH,EAkBGiC,WAlBH,CAkBgBjC,KAAD,IAAW;AACtB,QAAIS,qBAAqB,CAACR,OAA1B,EAAmC;AACjCe,MAAAA,wBAAwB,CAACf,OAAzB,GAAmC,MACjCS,eAAe,CAACxD,4BAA4B,CAAC8C,KAAD,CAA7B,CADjB;;AAEA;AACD,KALqB,CAMtB;AACA;;;AACA,QAAIO,oBAAoB,CAACN,OAArB,KAAiC,IAArC,EAA2C;AACzCX,MAAAA,0BAA0B,CAACW,OAA3B,GAAqC,IAArC;AACD;;AACDS,IAAAA,eAAe,CAACxD,4BAA4B,CAAC8C,KAAD,CAA7B,CAAf;AACD,GA9BH,EA+BGkC,kBA/BH,CA+BuBlC,KAAD,IAAW;AAC7BZ,IAAAA,sBAAsB,CAACa,OAAvB,GAAiC,KAAjC;;AAEA,QAAIQ,qBAAqB,CAACR,OAA1B,EAAmC;AACjCgB,MAAAA,iBAAiB,CAAChB,OAAlB,GAA4B,IAA5B;;AACAe,MAAAA,wBAAwB,CAACf,OAAzB,GAAmC,MACjCS,eAAe,CAACxD,4BAA4B,CAAC8C,KAAD,CAA7B,CADjB;;AAEA;AACD;;AAED,QACE,CAACX,qBAAqB,CAACY,OAAvB,IACAD,KAAK,CAACmC,UAAN,CAAiBrB,MAAjB,GAA0Bd,KAAK,CAACe,cAAN,CAAqBD,MAFjD,EAGE;AACA;AACD;;AAEDJ,IAAAA,eAAe,CAACxD,4BAA4B,CAAC8C,KAAD,CAA7B,CAAf;AACD,GAjDH,CAFgC,EAoDlC,CAACnC,YAAD,EAAewD,eAAf,EAAgCX,eAAhC,CApDkC,CAApC,CA1P2D,CAiT3D;;AACA,QAAM0B,aAAa,GAAG9F,OAAO,CAC3B,MACEI,OAAO,CAAC2F,MAAR,GACGtC,OADH,CACW,MAAM;AACb;AACA,QAAInD,QAAQ,CAAC0F,EAAT,KAAgB,SAAhB,IAA6B1F,QAAQ,CAAC0F,EAAT,KAAgB,OAAjD,EAA0D;AACxDhC,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACD;AACF,GANH,EAOGsC,OAPH,CAOW,MAAM;AACb,QAAI3F,QAAQ,CAAC0F,EAAT,KAAgB,KAApB,EAA2B;AACzBhC,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACD,KAHY,CAKb;;;AACA,QAAIrD,QAAQ,CAAC0F,EAAT,KAAgB,KAApB,EAA2B;AACzB;AACD;;AAED,QAAI/B,oBAAoB,CAACN,OAAzB,EAAkC;AAChCK,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;;AAEA,UAAIZ,qBAAqB,CAACY,OAA1B,EAAmC;AACjCO,QAAAA,cAAc,CAACD,oBAAoB,CAACN,OAAtB,CAAd;AACAM,QAAAA,oBAAoB,CAACN,OAArB,GAA+B,IAA/B;AACD,OAHD,MAGO;AACLS,QAAAA,eAAe,CAACH,oBAAoB,CAACN,OAAtB,CAAf;AACAK,QAAAA,yBAAyB,CAACL,OAA1B,GAAoC,KAApC;AACD;;AAED;AACD;;AAED,QAAIZ,qBAAqB,CAACY,OAA1B,EAAmC;AACjCK,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACA;AACD;;AAED,QAAIX,0BAA0B,CAACW,OAA/B,EAAwC;AACtCX,MAAAA,0BAA0B,CAACW,OAA3B,GAAqC,KAArC;;AACA,UAAI,CAACQ,qBAAqB,CAACR,OAA3B,EAAoC;AAClC;AACD;AACF;;AAEDK,IAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACD,GA5CH,CAFyB,EA+C3B,CAACO,cAAD,EAAiBE,eAAjB,CA/C2B,CAA7B;AAkDA,QAAM8B,cAAc,GAAGrF,SAAS,CAC9BoC,iBAD8B,EAE9BC,8BAF8B,CAAhC;AAKA,QAAMiD,kBAAkB,GAAG1D,QAAQ,KAAK,IAAxC;AAEA,QAAM2D,QAAQ,GAAG,CAACN,aAAD,EAAgBX,oBAAhB,EAAsC9B,YAAtC,CAAjB;;AAEA,OAAK,MAAMgD,OAAX,IAAsBD,QAAtB,EAAgC;AAC9BC,IAAAA,OAAO,CAACC,OAAR,CAAgBH,kBAAhB;AACAE,IAAAA,OAAO,CAACE,OAAR,CAAgB,IAAhB;AACAF,IAAAA,OAAO,CAAC5E,OAAR,CAAgByE,cAAhB;AACAG,IAAAA,OAAO,CAACG,uBAAR,CAAgClG,QAAQ,CAAC0F,EAAT,KAAgB,KAAhB,GAAwB,KAAxB,GAAgC,IAAhE;AACD,GAlX0D,CAoX3D;;;AACAF,EAAAA,aAAa,CAACrE,OAAd,CAAsBwB,iBAAtB;AAEA,QAAMoD,OAAO,GAAGjG,OAAO,CAACqG,YAAR,CAAqB,GAAGL,QAAxB,CAAhB,CAvX2D,CAyX3D;;AACA,QAAMM,YAAkC,GACtCpG,QAAQ,CAAC0F,EAAT,KAAgB,KAAhB,GAAwB;AAAEW,IAAAA,MAAM,EAAE;AAAV,GAAxB,GAAgD,EADlD;AAGA,QAAMC,SAAS,GACb,OAAOvE,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,CAAC;AAAEwE,IAAAA,OAAO,EAAEjE;AAAX,GAAD,CAAnC,GAAiEP,KADnE;AAGA,QAAMyE,YAAY,GAChB,OAAOxE,QAAP,KAAoB,UAApB,GACIA,QAAQ,CAAC;AAAEuE,IAAAA,OAAO,EAAEjE;AAAX,GAAD,CADZ,GAEIN,QAHN;AAKA,QAAMyE,WAAW,GAAG/G,OAAO,CAAC,MAAM;AAAA;;AAChC,QAAIoB,SAAS,KAAK,IAAlB,EAAwB;AACtBA,MAAAA,SAAS,GAAGJ,QAAQ,EAApB;AACD;;AAED,UAAMgG,kBAAkB,GAAGxE,cAAc,GAAGyE,SAAH,GAAe,aAAxD;AACA,UAAMC,sBAAsB,4BAC1B1E,cAD0B,aAC1BA,cAD0B,uBAC1BA,cAAc,CAAE2E,KADU,yEACDH,kBAD3B;AAEA,WAAO5F,SAAS,GACZ8F,sBADY,GAEZ3G,YAAY,CAAC2G,sBAAD,CAFhB;AAGD,GAX0B,EAWxB,CAAC1E,cAAD,CAXwB,CAA3B;AAaA,sBACE,oBAAC,eAAD;AAAiB,IAAA,OAAO,EAAE6D;AAA1B,kBACE,oBAAC,YAAD,eACM1D,cADN;AAEE,IAAA,GAAG,EAAEpB,YAAF,aAAEA,YAAF,cAAEA,YAAF,GAAkBuD,iBAFvB;AAGE,IAAA,UAAU,EAAEpC,UAAU,KAAK,KAH7B;AAIE,IAAA,OAAO,EAAEwD,cAJX;AAKE,IAAA,OAAO,EAAEC,kBALX;AAME,IAAA,kBAAkB,EAAE5D,oBAAF,aAAEA,oBAAF,cAAEA,oBAAF,GAA0B0E,SAN9C;AAOE,IAAA,WAAW,EAAEF,WAPf;AAQE,IAAA,YAAY,2BAAEvE,cAAF,aAAEA,cAAF,uBAAEA,cAAc,CAAE4E,MAAlB,yEAA4BH,SAR1C;AASE,IAAA,KAAK,EAAE,CAACP,YAAD,EAAeE,SAAf,CATT;AAUE,IAAA,gBAAgB,EAAEzF,WAAW,GAAGc,OAAH,GAAagF,SAV5C;AAWE,IAAA,kBAAkB,EAAE9F,WAAW,GAAGe,SAAH,GAAe+E,SAXhD;AAYE,IAAA,mBAAmB,EAAE9F,WAAW,GAAGgB,UAAH,GAAgB8E,SAZlD;AAaE,IAAA,oBAAoB,EAAE9F,WAAW,GAAGiB,WAAH,GAAiB6E;AAbpD,MAcGH,YAdH,EAeGO,OAAO,gBACN,oBAAC,qBAAD;AAAuB,IAAA,KAAK,EAAC,KAA7B;AAAmC,IAAA,OAAO,EAAEpE;AAA5C,IADM,GAEJ,IAjBN,CADF,CADF;AAuBD,CA1ayB,CAA5B;AA6aA,eAAe5B,SAAf", "sourcesContent": ["import React, {\n  ForwardedRef,\n  forwardRef,\n  RefObject,\n  useCallback,\n  useMemo,\n  useRef,\n  useState,\n} from 'react';\nimport { GestureObjects as Gesture } from '../../handlers/gestures/gestureObjects';\nimport { GestureDetector } from '../../handlers/gestures/GestureDetector';\nimport { PressableEvent, PressableProps } from './PressableProps';\nimport {\n  Insets,\n  Platform,\n  StyleProp,\n  View,\n  ViewStyle,\n  processColor,\n} from 'react-native';\nimport NativeButton from '../GestureHandlerButton';\nimport {\n  numberAsInset,\n  gestureToPressableEvent,\n  isTouchWithinInset,\n  gestureTouchToPressableEvent,\n  addInsets,\n} from './utils';\nimport { PressabilityDebugView } from '../../handlers/PressabilityDebugView';\nimport { GestureTouchEvent } from '../../handlers/gestureHandlerCommon';\nimport { INT32_MAX, isFabric, isTestEnv } from '../../utils';\n\nconst DEFAULT_LONG_PRESS_DURATION = 500;\nconst IS_TEST_ENV = isTestEnv();\n\nlet IS_FABRIC: null | boolean = null;\n\nconst Pressable = forwardRef(\n  (props: PressableProps, pressableRef: ForwardedRef<View>) => {\n    const {\n      testOnly_pressed,\n      hitSlop,\n      pressRetentionOffset,\n      delayHoverIn,\n      onHoverIn,\n      delayHoverOut,\n      onHoverOut,\n      delayLongPress,\n      unstable_pressDelay,\n      onPress,\n      onPressIn,\n      onPressOut,\n      onLongPress,\n      style,\n      children,\n      android_disableSound,\n      android_ripple,\n      disabled,\n      accessible,\n      ...remainingProps\n    } = props;\n\n    const [pressedState, setPressedState] = useState(testOnly_pressed ?? false);\n\n    // Disabled when onLongPress has been called\n    const isPressCallbackEnabled = useRef<boolean>(true);\n    const hasPassedBoundsChecks = useRef<boolean>(false);\n    const shouldPreventNativeEffects = useRef<boolean>(false);\n\n    const normalizedHitSlop: Insets = useMemo(\n      () =>\n        typeof hitSlop === 'number' ? numberAsInset(hitSlop) : (hitSlop ?? {}),\n      [hitSlop]\n    );\n\n    const normalizedPressRetentionOffset: Insets = useMemo(\n      () =>\n        typeof pressRetentionOffset === 'number'\n          ? numberAsInset(pressRetentionOffset)\n          : (pressRetentionOffset ?? {}),\n      [pressRetentionOffset]\n    );\n\n    const hoverInTimeout = useRef<number | null>(null);\n    const hoverOutTimeout = useRef<number | null>(null);\n\n    const hoverGesture = useMemo(\n      () =>\n        Gesture.Hover()\n          .manualActivation(true) // Stops Hover from blocking Native gesture activation on web\n          .cancelsTouchesInView(false)\n          .onBegin((event) => {\n            if (hoverOutTimeout.current) {\n              clearTimeout(hoverOutTimeout.current);\n            }\n            if (delayHoverIn) {\n              hoverInTimeout.current = setTimeout(\n                () => onHoverIn?.(gestureToPressableEvent(event)),\n                delayHoverIn\n              );\n              return;\n            }\n            onHoverIn?.(gestureToPressableEvent(event));\n          })\n          .onFinalize((event) => {\n            if (hoverInTimeout.current) {\n              clearTimeout(hoverInTimeout.current);\n            }\n            if (delayHoverOut) {\n              hoverOutTimeout.current = setTimeout(\n                () => onHoverOut?.(gestureToPressableEvent(event)),\n                delayHoverOut\n              );\n              return;\n            }\n            onHoverOut?.(gestureToPressableEvent(event));\n          }),\n      [delayHoverIn, delayHoverOut, onHoverIn, onHoverOut]\n    );\n\n    const pressDelayTimeoutRef = useRef<number | null>(null);\n    const isTouchPropagationAllowed = useRef<boolean>(false);\n\n    // iOS only: due to varying flow of gestures, events sometimes have to be saved for later use\n    const deferredEventPayload = useRef<PressableEvent | null>(null);\n\n    const pressInHandler = useCallback(\n      (event: PressableEvent) => {\n        if (handlingOnTouchesDown.current) {\n          deferredEventPayload.current = event;\n        }\n\n        if (!isTouchPropagationAllowed.current) {\n          return;\n        }\n\n        deferredEventPayload.current = null;\n\n        onPressIn?.(event);\n        isPressCallbackEnabled.current = true;\n        pressDelayTimeoutRef.current = null;\n        setPressedState(true);\n      },\n      [onPressIn]\n    );\n\n    const pressOutHandler = useCallback(\n      (event: PressableEvent) => {\n        if (!isTouchPropagationAllowed.current) {\n          hasPassedBoundsChecks.current = false;\n          isPressCallbackEnabled.current = true;\n          deferredEventPayload.current = null;\n\n          if (longPressTimeoutRef.current) {\n            clearTimeout(longPressTimeoutRef.current);\n            longPressTimeoutRef.current = null;\n          }\n\n          if (pressDelayTimeoutRef.current) {\n            clearTimeout(pressDelayTimeoutRef.current);\n            pressDelayTimeoutRef.current = null;\n          }\n\n          return;\n        }\n\n        if (\n          !hasPassedBoundsChecks.current ||\n          event.nativeEvent.touches.length >\n            event.nativeEvent.changedTouches.length\n        ) {\n          return;\n        }\n\n        if (unstable_pressDelay && pressDelayTimeoutRef.current !== null) {\n          // When delay is preemptively finished by lifting touches,\n          // we want to immediately activate it's effects - pressInHandler,\n          // even though we are located at the pressOutHandler\n          clearTimeout(pressDelayTimeoutRef.current);\n          pressInHandler(event);\n        }\n\n        if (deferredEventPayload.current) {\n          onPressIn?.(deferredEventPayload.current);\n          deferredEventPayload.current = null;\n        }\n\n        onPressOut?.(event);\n\n        if (isPressCallbackEnabled.current) {\n          onPress?.(event);\n        }\n\n        if (longPressTimeoutRef.current) {\n          clearTimeout(longPressTimeoutRef.current);\n          longPressTimeoutRef.current = null;\n        }\n\n        isTouchPropagationAllowed.current = false;\n        hasPassedBoundsChecks.current = false;\n        isPressCallbackEnabled.current = true;\n        setPressedState(false);\n      },\n      [onPress, onPressIn, onPressOut, pressInHandler, unstable_pressDelay]\n    );\n\n    const handlingOnTouchesDown = useRef<boolean>(false);\n    const onEndHandlingTouchesDown = useRef<(() => void) | null>(null);\n    const cancelledMidPress = useRef<boolean>(false);\n\n    const activateLongPress = useCallback(\n      (event: GestureTouchEvent) => {\n        if (!isTouchPropagationAllowed.current) {\n          return;\n        }\n\n        if (hasPassedBoundsChecks.current) {\n          onLongPress?.(gestureTouchToPressableEvent(event));\n          isPressCallbackEnabled.current = false;\n        }\n\n        if (longPressTimeoutRef.current) {\n          clearTimeout(longPressTimeoutRef.current);\n          longPressTimeoutRef.current = null;\n        }\n      },\n      [onLongPress]\n    );\n\n    const longPressTimeoutRef = useRef<number | null>(null);\n    const longPressMinDuration =\n      (delayLongPress ?? DEFAULT_LONG_PRESS_DURATION) +\n      (unstable_pressDelay ?? 0);\n\n    const innerPressableRef = useRef<View>(null);\n\n    const measureCallback = useCallback(\n      (width: number, height: number, event: GestureTouchEvent) => {\n        if (\n          !isTouchWithinInset(\n            {\n              width,\n              height,\n            },\n            normalizedHitSlop,\n            event.changedTouches.at(-1)\n          ) ||\n          hasPassedBoundsChecks.current ||\n          cancelledMidPress.current\n        ) {\n          cancelledMidPress.current = false;\n          onEndHandlingTouchesDown.current = null;\n          handlingOnTouchesDown.current = false;\n          return;\n        }\n\n        hasPassedBoundsChecks.current = true;\n\n        // In case of multiple touches, the first one starts long press gesture\n        if (longPressTimeoutRef.current === null) {\n          // Start long press gesture timer\n          longPressTimeoutRef.current = setTimeout(\n            () => activateLongPress(event),\n            longPressMinDuration\n          );\n        }\n\n        if (unstable_pressDelay) {\n          pressDelayTimeoutRef.current = setTimeout(() => {\n            pressInHandler(gestureTouchToPressableEvent(event));\n          }, unstable_pressDelay);\n        } else {\n          pressInHandler(gestureTouchToPressableEvent(event));\n        }\n\n        onEndHandlingTouchesDown.current?.();\n        onEndHandlingTouchesDown.current = null;\n        handlingOnTouchesDown.current = false;\n      },\n      [\n        activateLongPress,\n        longPressMinDuration,\n        normalizedHitSlop,\n        pressInHandler,\n        unstable_pressDelay,\n      ]\n    );\n\n    const pressAndTouchGesture = useMemo(\n      () =>\n        Gesture.LongPress()\n          .minDuration(INT32_MAX) // Stops long press from blocking native gesture\n          .maxDistance(INT32_MAX) // Stops long press from cancelling after set distance\n          .cancelsTouchesInView(false)\n          .onTouchesDown((event) => {\n            handlingOnTouchesDown.current = true;\n            if (pressableRef) {\n              (pressableRef as RefObject<View>).current?.measure(\n                (_x, _y, width, height) => {\n                  measureCallback(width, height, event);\n                }\n              );\n            } else {\n              innerPressableRef.current?.measure((_x, _y, width, height) => {\n                measureCallback(width, height, event);\n              });\n            }\n          })\n          .onTouchesUp((event) => {\n            if (handlingOnTouchesDown.current) {\n              onEndHandlingTouchesDown.current = () =>\n                pressOutHandler(gestureTouchToPressableEvent(event));\n              return;\n            }\n            // On iOS, short taps will make LongPress gesture call onTouchesUp before Native gesture calls onStart\n            // This variable ensures that onStart isn't detected as the first gesture since Pressable is pressed.\n            if (deferredEventPayload.current !== null) {\n              shouldPreventNativeEffects.current = true;\n            }\n            pressOutHandler(gestureTouchToPressableEvent(event));\n          })\n          .onTouchesCancelled((event) => {\n            isPressCallbackEnabled.current = false;\n\n            if (handlingOnTouchesDown.current) {\n              cancelledMidPress.current = true;\n              onEndHandlingTouchesDown.current = () =>\n                pressOutHandler(gestureTouchToPressableEvent(event));\n              return;\n            }\n\n            if (\n              !hasPassedBoundsChecks.current ||\n              event.allTouches.length > event.changedTouches.length\n            ) {\n              return;\n            }\n\n            pressOutHandler(gestureTouchToPressableEvent(event));\n          }),\n      [pressableRef, measureCallback, pressOutHandler]\n    );\n\n    // RNButton is placed inside ButtonGesture to enable Android's ripple and to capture non-propagating events\n    const buttonGesture = useMemo(\n      () =>\n        Gesture.Native()\n          .onBegin(() => {\n            // Android sets BEGAN state on press down\n            if (Platform.OS === 'android' || Platform.OS === 'macos') {\n              isTouchPropagationAllowed.current = true;\n            }\n          })\n          .onStart(() => {\n            if (Platform.OS === 'web') {\n              isTouchPropagationAllowed.current = true;\n            }\n\n            // iOS sets ACTIVE state on press down\n            if (Platform.OS !== 'ios') {\n              return;\n            }\n\n            if (deferredEventPayload.current) {\n              isTouchPropagationAllowed.current = true;\n\n              if (hasPassedBoundsChecks.current) {\n                pressInHandler(deferredEventPayload.current);\n                deferredEventPayload.current = null;\n              } else {\n                pressOutHandler(deferredEventPayload.current);\n                isTouchPropagationAllowed.current = false;\n              }\n\n              return;\n            }\n\n            if (hasPassedBoundsChecks.current) {\n              isTouchPropagationAllowed.current = true;\n              return;\n            }\n\n            if (shouldPreventNativeEffects.current) {\n              shouldPreventNativeEffects.current = false;\n              if (!handlingOnTouchesDown.current) {\n                return;\n              }\n            }\n\n            isTouchPropagationAllowed.current = true;\n          }),\n      [pressInHandler, pressOutHandler]\n    );\n\n    const appliedHitSlop = addInsets(\n      normalizedHitSlop,\n      normalizedPressRetentionOffset\n    );\n\n    const isPressableEnabled = disabled !== true;\n\n    const gestures = [buttonGesture, pressAndTouchGesture, hoverGesture];\n\n    for (const gesture of gestures) {\n      gesture.enabled(isPressableEnabled);\n      gesture.runOnJS(true);\n      gesture.hitSlop(appliedHitSlop);\n      gesture.shouldCancelWhenOutside(Platform.OS === 'web' ? false : true);\n    }\n\n    // Uses different hitSlop, to activate on hitSlop area instead of pressRetentionOffset area\n    buttonGesture.hitSlop(normalizedHitSlop);\n\n    const gesture = Gesture.Simultaneous(...gestures);\n\n    // `cursor: 'pointer'` on `RNButton` crashes iOS\n    const pointerStyle: StyleProp<ViewStyle> =\n      Platform.OS === 'web' ? { cursor: 'pointer' } : {};\n\n    const styleProp =\n      typeof style === 'function' ? style({ pressed: pressedState }) : style;\n\n    const childrenProp =\n      typeof children === 'function'\n        ? children({ pressed: pressedState })\n        : children;\n\n    const rippleColor = useMemo(() => {\n      if (IS_FABRIC === null) {\n        IS_FABRIC = isFabric();\n      }\n\n      const defaultRippleColor = android_ripple ? undefined : 'transparent';\n      const unprocessedRippleColor =\n        android_ripple?.color ?? defaultRippleColor;\n      return IS_FABRIC\n        ? unprocessedRippleColor\n        : processColor(unprocessedRippleColor);\n    }, [android_ripple]);\n\n    return (\n      <GestureDetector gesture={gesture}>\n        <NativeButton\n          {...remainingProps}\n          ref={pressableRef ?? innerPressableRef}\n          accessible={accessible !== false}\n          hitSlop={appliedHitSlop}\n          enabled={isPressableEnabled}\n          touchSoundDisabled={android_disableSound ?? undefined}\n          rippleColor={rippleColor}\n          rippleRadius={android_ripple?.radius ?? undefined}\n          style={[pointerStyle, styleProp]}\n          testOnly_onPress={IS_TEST_ENV ? onPress : undefined}\n          testOnly_onPressIn={IS_TEST_ENV ? onPressIn : undefined}\n          testOnly_onPressOut={IS_TEST_ENV ? onPressOut : undefined}\n          testOnly_onLongPress={IS_TEST_ENV ? onLongPress : undefined}>\n          {childrenProp}\n          {__DEV__ ? (\n            <PressabilityDebugView color=\"red\" hitSlop={normalizedHitSlop} />\n          ) : null}\n        </NativeButton>\n      </GestureDetector>\n    );\n  }\n);\n\nexport default Pressable;\n"]}