# .-. .-. .-. . . .-. .-. .-. .-.
# |(  |-  |.| | | |-  `-.  |  `-.
# ' ' `-' `-`.`-' `-' `-'  '  `-'

from __future__ import annotations

__title__: str = "niquests"
__description__: str = "Python HTTP for Humans."
__url__: str = "https://niquests.readthedocs.io"

__version__: str
__version__ = "3.14.1"

__build__: int = 0x031401
__author__: str = "<PERSON>"
__author_email__: str = "<EMAIL>"
__license__: str = "Apache-2.0"
__copyright__: str = "Copyright Kenneth <PERSON>"
__cake__: str = "\u2728 \U0001f370 \u2728"
