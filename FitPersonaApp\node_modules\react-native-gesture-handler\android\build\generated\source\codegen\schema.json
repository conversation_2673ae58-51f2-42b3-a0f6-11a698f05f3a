{"modules": {"NativeRNGestureHandlerModule": {"type": "NativeModule", "aliasMap": {}, "enumMap": {}, "spec": {"eventEmitters": [], "methods": [{"name": "handleSetJSResponder", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": [{"name": "tag", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "blockNativeResponder", "optional": false, "typeAnnotation": {"type": "BooleanTypeAnnotation"}}]}}, {"name": "handleClearJSResponder", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": []}}, {"name": "createGestureHandler", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": [{"name": "handler<PERSON>ame", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}, {"name": "handlerTag", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "config", "optional": false, "typeAnnotation": {"type": "GenericObjectTypeAnnotation"}}]}}, {"name": "attachGestureHandler", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": [{"name": "handlerTag", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "newView", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "actionType", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}]}}, {"name": "updateGestureHandler", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": [{"name": "handlerTag", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "newConfig", "optional": false, "typeAnnotation": {"type": "GenericObjectTypeAnnotation"}}]}}, {"name": "dropGestureHandler", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": [{"name": "handlerTag", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}]}}, {"name": "install", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "BooleanTypeAnnotation"}, "params": []}}, {"name": "flushOperations", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": []}}]}, "moduleName": "RNGestureHandlerModule"}, "RNGestureHandlerButton": {"type": "Component", "components": {"RNGestureHandlerButton": {"extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "exclusive", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": true}}, {"name": "foreground", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "borderless", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "enabled", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": true}}, {"name": "rippleColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "rippleRadius", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "touchSoundDisabled", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "borderWidth", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "borderColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "borderStyle", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": "solid"}}], "commands": []}}}, "RNGestureHandlerRootView": {"type": "Component", "components": {"RNGestureHandlerRootView": {"extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [], "commands": []}}}}}