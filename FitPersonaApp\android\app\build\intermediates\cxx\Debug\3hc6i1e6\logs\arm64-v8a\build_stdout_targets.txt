ninja: Entering directory `C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\android\app\.cxx\Debug\3hc6i1e6\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/43] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o
[2/43] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/333aa493d90404bf8b2b07d02055209c/safeareacontext/States.cpp.o
[3/43] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o
[4/43] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/333aa493d90404bf8b2b07d02055209c/safeareacontext/EventEmitters.cpp.o
[5/43] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/661528751d02f030f586d183e131b46d/safeareacontextJSI-generated.cpp.o
[6/43] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[7/43] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
[8/43] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/60f8ca999870cb6d1135ca54b4e32512/jni/safeareacontext-generated.cpp.o
[9/43] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o
[10/43] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[11/43] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/96efa0d603aaa84b5b7d47faebbf90c9/RNCSafeAreaViewState.cpp.o
[12/43] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/333aa493d90404bf8b2b07d02055209c/safeareacontext/ShadowNodes.cpp.o
[13/43] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/333aa493d90404bf8b2b07d02055209c/safeareacontext/Props.cpp.o
[14/43] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/661528751d02f030f586d183e131b46d/ComponentDescriptors.cpp.o
[15/43] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o
[16/43] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o
[17/43] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/96efa0d603aaa84b5b7d47faebbf90c9/RNCSafeAreaViewShadowNode.cpp.o
[18/43] Linking CXX shared library C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\android\app\build\intermediates\cxx\Debug\3hc6i1e6\obj\arm64-v8a\libreact_codegen_safeareacontext.so
[19/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3e7d7695ed4c01fe524a33d32a9ee352/components/rnscreens/RNSScreenState.cpp.o
[20/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/399fe1a10f92557e9c8266452189fad4/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[21/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8f62d86af600719754dda6dbf8d89821/RNSScreenStackHeaderSubviewState.cpp.o
[22/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/399fe1a10f92557e9c8266452189fad4/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[23/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3e7075b0aa02901f2917b768d8eb3346/renderer/components/rnscreens/States.cpp.o
[24/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8f62d86af600719754dda6dbf8d89821/RNSScreenStackHeaderConfigShadowNode.cpp.o
[25/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3e7d7695ed4c01fe524a33d32a9ee352/components/rnscreens/RNSScreenShadowNode.cpp.o
[26/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/399fe1a10f92557e9c8266452189fad4/rnscreens/RNSModalScreenShadowNode.cpp.o
[27/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/724ae0bc46f0a8755c3e847de44d1384/rnscreens/rnscreensJSI-generated.cpp.o
[28/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[29/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/8f62d86af600719754dda6dbf8d89821/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[30/43] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o
[31/43] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o
[32/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/3e7075b0aa02901f2917b768d8eb3346/renderer/components/rnscreens/ShadowNodes.cpp.o
[33/43] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o
[34/43] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o
[35/43] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/ComponentDescriptors.cpp.o
[36/43] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o
[37/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/97877ef4003c53ab54c46ae0766ac59a/react/renderer/components/rnscreens/Props.cpp.o
[38/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5ca29abb478ec8a56308d23e0113994f/components/rnscreens/EventEmitters.cpp.o
[39/43] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o
[40/43] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5ca29abb478ec8a56308d23e0113994f/components/rnscreens/ComponentDescriptors.cpp.o
[41/43] Linking CXX shared library C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\android\app\build\intermediates\cxx\Debug\3hc6i1e6\obj\arm64-v8a\libreact_codegen_rnscreens.so
[42/43] Building CXX object CMakeFiles/appmodules.dir/4c9de7f65867af90bf312ac6e18e5861/FitPersonaApp/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[43/43] Linking CXX shared library C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\android\app\build\intermediates\cxx\Debug\3hc6i1e6\obj\arm64-v8a\libappmodules.so
