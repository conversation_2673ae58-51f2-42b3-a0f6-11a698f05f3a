/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  class JSI_EXPORT NativeRNGestureHandlerModuleCxxSpecJSI : public TurboModule {
protected:
  NativeRNGestureHandlerModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void handleSetJSResponder(jsi::Runtime &rt, double tag, bool blockNativeResponder) = 0;
  virtual void handleClearJSResponder(jsi::Runtime &rt) = 0;
  virtual void createGestureHandler(jsi::Runtime &rt, jsi::String handlerName, double handlerTag, jsi::Object config) = 0;
  virtual void attachGestureHandler(jsi::Runtime &rt, double handlerTag, double newView, double actionType) = 0;
  virtual void updateGestureHandler(jsi::Runtime &rt, double handlerTag, jsi::Object newConfig) = 0;
  virtual void dropGestureHandler(jsi::Runtime &rt, double handlerTag) = 0;
  virtual bool install(jsi::Runtime &rt) = 0;
  virtual void flushOperations(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeRNGestureHandlerModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "RNGestureHandlerModule";

protected:
  NativeRNGestureHandlerModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeRNGestureHandlerModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeRNGestureHandlerModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeRNGestureHandlerModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void handleSetJSResponder(jsi::Runtime &rt, double tag, bool blockNativeResponder) override {
      static_assert(
          bridging::getParameterCount(&T::handleSetJSResponder) == 3,
          "Expected handleSetJSResponder(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::handleSetJSResponder, jsInvoker_, instance_, std::move(tag), std::move(blockNativeResponder));
    }
    void handleClearJSResponder(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::handleClearJSResponder) == 1,
          "Expected handleClearJSResponder(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::handleClearJSResponder, jsInvoker_, instance_);
    }
    void createGestureHandler(jsi::Runtime &rt, jsi::String handlerName, double handlerTag, jsi::Object config) override {
      static_assert(
          bridging::getParameterCount(&T::createGestureHandler) == 4,
          "Expected createGestureHandler(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::createGestureHandler, jsInvoker_, instance_, std::move(handlerName), std::move(handlerTag), std::move(config));
    }
    void attachGestureHandler(jsi::Runtime &rt, double handlerTag, double newView, double actionType) override {
      static_assert(
          bridging::getParameterCount(&T::attachGestureHandler) == 4,
          "Expected attachGestureHandler(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::attachGestureHandler, jsInvoker_, instance_, std::move(handlerTag), std::move(newView), std::move(actionType));
    }
    void updateGestureHandler(jsi::Runtime &rt, double handlerTag, jsi::Object newConfig) override {
      static_assert(
          bridging::getParameterCount(&T::updateGestureHandler) == 3,
          "Expected updateGestureHandler(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::updateGestureHandler, jsInvoker_, instance_, std::move(handlerTag), std::move(newConfig));
    }
    void dropGestureHandler(jsi::Runtime &rt, double handlerTag) override {
      static_assert(
          bridging::getParameterCount(&T::dropGestureHandler) == 2,
          "Expected dropGestureHandler(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::dropGestureHandler, jsInvoker_, instance_, std::move(handlerTag));
    }
    bool install(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::install) == 1,
          "Expected install(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::install, jsInvoker_, instance_);
    }
    void flushOperations(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::flushOperations) == 1,
          "Expected flushOperations(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::flushOperations, jsInvoker_, instance_);
    }

  private:
    friend class NativeRNGestureHandlerModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
