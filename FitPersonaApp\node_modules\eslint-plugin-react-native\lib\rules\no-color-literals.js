/**
 * @fileoverview Detects color literals
 * <AUTHOR>
 */

'use strict';

const util = require('util');
const Components = require('../util/Components');
const styleSheet = require('../util/stylesheet');

const { StyleSheets } = styleSheet;
const { astHelpers } = styleSheet;

const create = Components.detect((context) => {
  const styleSheets = new StyleSheets();

  function reportColorLiterals(colorLiterals) {
    if (colorLiterals) {
      colorLiterals.forEach((style) => {
        if (style) {
          const expression = util.inspect(style.expression);
          context.report({
            node: style.node,
            message: 'Color literal: {{expression}}',
            data: { expression },
          });
        }
      });
    }
  }

  return {
    CallExpression: (node) => {
      if (astHelpers.isStyleSheetDeclaration(node, context.settings)) {
        const styles = astHelpers.getStyleDeclarations(node);

        if (styles) {
          styles.forEach((style) => {
            const literals = astHelpers.collectColorLiter<PERSON>(style.value, context);
            styleSheets.addColorLiterals(literals);
          });
        }
      }
    },

    JSXAttribute: (node) => {
      if (astHelpers.isStyleAttribute(node)) {
        const literals = astHelpers.collectColorLiterals(node.value, context);
        styleSheets.addColorLiterals(literals);
      }
    },

    'Program:exit': () => reportColorLiterals(styleSheets.getColorLiterals()),
  };
});

module.exports = {
  meta: {
    schema: [],
  },
  create,
};
