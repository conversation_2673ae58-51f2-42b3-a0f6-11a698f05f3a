import { RedisCommandArgument, RedisCommandArguments } from '@redis/client/dist/lib/commands';
import { QueryOptionsBackwardCompatible } from '.';
export { FIRST_KEY_INDEX } from './QUERY';
export declare const IS_READ_ONLY = true;
export declare function transformArguments(graph: RedisCommandArgument, query: RedisCommandArgument, options?: QueryOptionsBackwardCompatible, compact?: boolean): RedisCommandArguments;
export { transformReply } from './QUERY';
