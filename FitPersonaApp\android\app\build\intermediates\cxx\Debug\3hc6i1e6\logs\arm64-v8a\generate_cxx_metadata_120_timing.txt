# C/C++ build system timings
generate_cxx_metadata
  [gap of 218ms]
  create-invalidation-state 72ms
  generate-prefab-packages
    [gap of 72ms]
    exec-prefab 3927ms
    [gap of 102ms]
  generate-prefab-packages completed in 4101ms
  execute-generate-process
    [gap of 20ms]
    exec-configure 16223ms
    [gap of 428ms]
  execute-generate-process completed in 16671ms
  [gap of 30ms]
  remove-unexpected-so-files 10ms
  [gap of 233ms]
  write-metadata-json-to-file 24ms
generate_cxx_metadata completed in 21425ms

