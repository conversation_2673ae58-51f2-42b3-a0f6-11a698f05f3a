/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Cohere from "../../../../index";
/**
 * @example
 *     {}
 */
export interface UpdateConnectorRequest {
    /** A human-readable name for the connector. */
    name?: string;
    /** The URL of the connector that will be used to search for documents. */
    url?: string;
    /** A list of fields to exclude from the prompt (fields remain in the document). */
    excludes?: string[];
    /** The OAuth 2.0 configuration for the connector. Cannot be specified if service_auth is specified. */
    oauth?: Cohere.CreateConnectorOAuth;
    active?: boolean;
    continueOnFailure?: boolean;
    /** The service to service authentication configuration for the connector. Cannot be specified if oauth is specified. */
    serviceAuth?: Cohere.CreateConnectorServiceAuth;
}
