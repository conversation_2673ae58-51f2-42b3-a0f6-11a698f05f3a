<?xml version="1.0" encoding="utf-8"?>
<!--Android Nougat open animation-->
<!--http://aosp.opersys.com/xref/android-7.1.2_r37/xref/frameworks/base/core/res/res/anim/activity_open_enter.xml-->
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:shareInterpolator="false">
    <alpha android:fromAlpha="0.0" android:toAlpha="1.0"
        android:interpolator="@android:interpolator/decelerate_quint"
        android:duration="210"/>
    <translate android:fromYDelta="8%" android:toYDelta="0"
        android:interpolator="@android:interpolator/decelerate_quint"
        android:duration="350"/> <!--we use rns_no_animation_350.xml as the other animation for
        this transition since we want both of them to end at the same time. Remember to change
        duration in both files when modifying it-->
</set>
