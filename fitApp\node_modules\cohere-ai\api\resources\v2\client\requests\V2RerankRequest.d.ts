/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * @example
 *     {
 *         model: "model",
 *         query: "query",
 *         documents: ["documents"]
 *     }
 */
export interface V2RerankRequest {
    /** The identifier of the model to use, eg `rerank-v3.5`. */
    model: string;
    /** The search query */
    query: string;
    /**
     * A list of texts that will be compared to the `query`.
     * For optimal performance we recommend against sending more than 1,000 documents in a single request.
     *
     * **Note**: long documents will automatically be truncated to the value of `max_tokens_per_doc`.
     *
     * **Note**: structured data should be formatted as YAML strings for best performance.
     */
    documents: string[];
    /** Limits the number of returned rerank results to the specified value. If not passed, all the rerank results will be returned. */
    topN?: number;
    /**
     * - If false, returns results without the doc text - the api will return a list of {index, relevance score} where index is inferred from the list passed into the request.
     * - If true, returns results with the doc text passed in - the api will return an ordered list of {index, text, relevance score} where index + text refers to the list passed into the request.
     */
    returnDocuments?: boolean;
    /** Defaults to `4096`. Long documents will be automatically truncated to the specified number of tokens. */
    maxTokensPerDoc?: number;
}
