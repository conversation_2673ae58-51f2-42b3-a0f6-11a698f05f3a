<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base"/><source path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\v33"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base"><file name="rns_default_enter_in" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_default_enter_in.xml" qualifiers="" type="anim"/><file name="rns_default_enter_out" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_default_enter_out.xml" qualifiers="" type="anim"/><file name="rns_default_exit_in" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_default_exit_in.xml" qualifiers="" type="anim"/><file name="rns_default_exit_out" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_default_exit_out.xml" qualifiers="" type="anim"/><file name="rns_fade_from_bottom" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_fade_from_bottom.xml" qualifiers="" type="anim"/><file name="rns_fade_in" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_fade_in.xml" qualifiers="" type="anim"/><file name="rns_fade_out" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_fade_out.xml" qualifiers="" type="anim"/><file name="rns_fade_to_bottom" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_fade_to_bottom.xml" qualifiers="" type="anim"/><file name="rns_ios_from_left_background_close" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_ios_from_left_background_close.xml" qualifiers="" type="anim"/><file name="rns_ios_from_left_background_open" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_ios_from_left_background_open.xml" qualifiers="" type="anim"/><file name="rns_ios_from_left_foreground_close" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_ios_from_left_foreground_close.xml" qualifiers="" type="anim"/><file name="rns_ios_from_left_foreground_open" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_ios_from_left_foreground_open.xml" qualifiers="" type="anim"/><file name="rns_ios_from_right_background_close" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_ios_from_right_background_close.xml" qualifiers="" type="anim"/><file name="rns_ios_from_right_background_open" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_ios_from_right_background_open.xml" qualifiers="" type="anim"/><file name="rns_ios_from_right_foreground_close" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_ios_from_right_foreground_close.xml" qualifiers="" type="anim"/><file name="rns_ios_from_right_foreground_open" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_ios_from_right_foreground_open.xml" qualifiers="" type="anim"/><file name="rns_no_animation_20" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_no_animation_20.xml" qualifiers="" type="anim"/><file name="rns_no_animation_250" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_no_animation_250.xml" qualifiers="" type="anim"/><file name="rns_no_animation_350" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_no_animation_350.xml" qualifiers="" type="anim"/><file name="rns_no_animation_medium" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_no_animation_medium.xml" qualifiers="" type="anim"/><file name="rns_slide_in_from_bottom" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_slide_in_from_bottom.xml" qualifiers="" type="anim"/><file name="rns_slide_in_from_left" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_slide_in_from_left.xml" qualifiers="" type="anim"/><file name="rns_slide_in_from_right" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_slide_in_from_right.xml" qualifiers="" type="anim"/><file name="rns_slide_out_to_bottom" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_slide_out_to_bottom.xml" qualifiers="" type="anim"/><file name="rns_slide_out_to_left" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_slide_out_to_left.xml" qualifiers="" type="anim"/><file name="rns_slide_out_to_right" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_slide_out_to_right.xml" qualifiers="" type="anim"/><file name="rns_standard_accelerate_interpolator" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\anim\rns_standard_accelerate_interpolator.xml" qualifiers="" type="anim"/><file name="rns_rounder_top_corners_shape" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\base\drawable\rns_rounder_top_corners_shape.xml" qualifiers="" type="drawable"/></source><source path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\v33"><file name="rns_default_enter_in" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\v33\anim-v33\rns_default_enter_in.xml" qualifiers="v33" type="anim"/><file name="rns_default_enter_out" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\v33\anim-v33\rns_default_enter_out.xml" qualifiers="v33" type="anim"/><file name="rns_default_exit_in" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\v33\anim-v33\rns_default_exit_in.xml" qualifiers="v33" type="anim"/><file name="rns_default_exit_out" path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\main\res\v33\anim-v33\rns_default_exit_out.xml" qualifiers="v33" type="anim"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Weather-Forecast-MCP-main\FitPersonaApp\node_modules\react-native-screens\android\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>