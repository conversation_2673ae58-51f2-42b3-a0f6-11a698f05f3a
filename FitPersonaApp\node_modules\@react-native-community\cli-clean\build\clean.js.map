{"version": 3, "names": ["DEFAULT_GROUPS", "rmAsync", "promisify", "rm", "rmAsyncOptions", "maxRetries", "recursive", "force", "isDirectoryPattern", "directory", "endsWith", "cleanDir", "directories", "glob", "async", "onlyFiles", "dir", "fileExists", "error", "logger", "promptForCaches", "groups", "caches", "prompt", "type", "name", "message", "choices", "Object", "entries", "map", "cmd", "group", "title", "chalk", "dim", "description", "value", "selected", "includes", "min", "clean", "_argv", "ctx", "cleanOptions", "include", "projectRoot", "verifyCache", "Error", "COMMANDS", "android", "tasks", "label", "action", "gradlew", "os", "platform", "path", "join", "project", "sourceDir", "script", "basename", "execa", "cwd", "dirname", "cocoapods", "undefined", "metro", "tmpdir", "bun", "watchman", "yarn", "npm", "split", "length", "spinner", "<PERSON><PERSON><PERSON><PERSON>", "commands", "warn", "start", "then", "succeed", "catch", "e", "fail", "func", "options", "default", "process"], "sources": ["../src/clean.ts"], "sourcesContent": ["import {getLoader, logger, prompt} from '@react-native-community/cli-tools';\nimport type {Config as CLIConfig} from '@react-native-community/cli-types';\nimport chalk from 'chalk';\nimport execa from 'execa';\nimport {existsSync as fileExists, rm} from 'fs';\nimport os from 'os';\nimport path from 'path';\nimport {promisify} from 'util';\nimport glob from 'fast-glob';\n\ntype Args = {\n  include?: string;\n  projectRoot: string;\n  verifyCache?: boolean;\n};\n\ntype Task = {\n  label: string;\n  action: () => Promise<void>;\n};\n\ntype CleanGroups = {\n  [key: string]: {\n    description: string;\n    tasks: Task[];\n  };\n};\n\nconst DEFAULT_GROUPS = ['metro', 'watchman'];\n\nconst rmAsync = promisify(rm);\nconst rmAsyncOptions = {maxRetries: 3, recursive: true, force: true};\n\nfunction isDirectoryPattern(directory: string): boolean {\n  return directory.endsWith('*') || directory.endsWith('?');\n}\n\nexport async function cleanDir(directory: string): Promise<void> {\n  try {\n    if (isDirectoryPattern(directory)) {\n      const directories = await glob.async(directory, {onlyFiles: false});\n\n      for (const dir of directories) {\n        await rmAsync(dir, rmAsyncOptions);\n      }\n    } else {\n      if (!fileExists(directory)) {\n        return;\n      }\n      await rmAsync(directory, rmAsyncOptions);\n    }\n  } catch (error) {\n    logger.error(`An error occurred while cleaning the directory: ${error}`);\n  }\n}\n\nasync function promptForCaches(\n  groups: CleanGroups,\n): Promise<string[] | undefined> {\n  const {caches} = await prompt({\n    type: 'multiselect',\n    name: 'caches',\n    message: 'Select all caches to clean',\n    choices: Object.entries(groups).map(([cmd, group]) => ({\n      title: `${cmd} ${chalk.dim(`(${group.description})`)}`,\n      value: cmd,\n      selected: DEFAULT_GROUPS.includes(cmd),\n    })),\n    min: 1,\n  });\n  return caches;\n}\n\nexport async function clean(\n  _argv: string[],\n  ctx: CLIConfig,\n  cleanOptions: Args,\n): Promise<void> {\n  const {include, projectRoot, verifyCache} = cleanOptions;\n  if (!fileExists(projectRoot)) {\n    throw new Error(`Invalid path provided! ${projectRoot}`);\n  }\n\n  const COMMANDS: CleanGroups = {\n    android: {\n      description: 'Android build caches, e.g. Gradle',\n      tasks: [\n        {\n          label: 'Clean Gradle cache',\n          action: async () => {\n            const gradlew =\n              os.platform() === 'win32'\n                ? path.join(\n                    ctx.project.android?.sourceDir ?? 'android',\n                    'gradlew.bat',\n                  )\n                : path.join(\n                    ctx.project.android?.sourceDir ?? 'android',\n                    'gradlew',\n                  );\n\n            if (fileExists(gradlew)) {\n              const script = path.basename(gradlew);\n              await execa(\n                os.platform() === 'win32' ? script : `./${script}`,\n                ['clean'],\n                {cwd: path.dirname(gradlew)},\n              );\n            }\n          },\n        },\n      ],\n    },\n    ...(os.platform() === 'darwin'\n      ? {\n          cocoapods: {\n            description: 'CocoaPods cache',\n            tasks: [\n              {\n                label: 'Clean CocoaPods pod cache',\n                action: async () => {\n                  await execa('pod', ['cache', 'clean', '--all'], {\n                    cwd: projectRoot,\n                  });\n                },\n              },\n              {\n                label: 'Remove installed CocoaPods',\n                action: () => cleanDir('ios/Pods'),\n              },\n              {\n                label: 'Remove CocoaPods spec cache',\n                action: () => cleanDir('~/.cocoapods'),\n              },\n            ],\n          },\n        }\n      : undefined),\n    metro: {\n      description: 'Metro, haste-map caches',\n      tasks: [\n        {\n          label: 'Clean Metro cache',\n          action: () => cleanDir(`${os.tmpdir()}/metro-*`),\n        },\n        {\n          label: 'Clean Haste cache',\n          action: () => cleanDir(`${os.tmpdir()}/haste-map-*`),\n        },\n        {\n          label: 'Clean React Native cache',\n          action: () => cleanDir(`${os.tmpdir()}/react-*`),\n        },\n      ],\n    },\n    bun: {\n      description: 'Bun cache',\n      tasks: [\n        {\n          label: 'Clean Bun cache',\n          action: async () => {\n            await execa('bun', ['pm', 'cache', 'rm'], {cwd: projectRoot});\n          },\n        },\n      ],\n    },\n    watchman: {\n      description: 'Stop Watchman and delete its cache',\n      tasks: [\n        {\n          label: 'Stop Watchman',\n          action: async () => {\n            await execa(\n              os.platform() === 'win32' ? 'tskill' : 'killall',\n              ['watchman'],\n              {cwd: projectRoot},\n            );\n          },\n        },\n        {\n          label: 'Delete Watchman cache',\n          action: async () => {\n            await execa('watchman', ['watch-del-all'], {cwd: projectRoot});\n          },\n        },\n      ],\n    },\n    yarn: {\n      description: 'Yarn cache',\n      tasks: [\n        {\n          label: 'Clean Yarn cache',\n          action: async () => {\n            await execa('yarn', ['cache', 'clean'], {cwd: projectRoot});\n          },\n        },\n      ],\n    },\n    npm: {\n      description:\n        '`node_modules` folder in the current package, and optionally verify npm cache',\n      tasks: [\n        {\n          label: 'Remove node_modules',\n          action: () => cleanDir(`${projectRoot}/node_modules`),\n        },\n        ...(verifyCache\n          ? [\n              {\n                label: 'Verify npm cache',\n                action: async () => {\n                  await execa('npm', ['cache', 'verify'], {cwd: projectRoot});\n                },\n              },\n            ]\n          : []),\n      ],\n    },\n  };\n\n  const groups = include ? include.split(',') : await promptForCaches(COMMANDS);\n  if (!groups || groups.length === 0) {\n    return;\n  }\n\n  const spinner = getLoader();\n  for (const group of groups) {\n    const commands = COMMANDS[group];\n    if (!commands) {\n      spinner.warn(`Unknown group: ${group}`);\n      continue;\n    }\n\n    for (const {action, label} of commands.tasks) {\n      spinner.start(label);\n      await action()\n        .then(() => {\n          spinner.succeed();\n        })\n        .catch((e) => {\n          spinner.fail(`${label} » ${e}`);\n        });\n    }\n  }\n}\n\nexport default {\n  func: clean,\n  name: 'clean',\n  description:\n    'Cleans your project by removing React Native related caches and modules.',\n  options: [\n    {\n      name: '--include <string>',\n      description:\n        'Comma-separated flag of caches to clear e.g. `npm,yarn`. If omitted, an interactive prompt will appear.',\n    },\n    {\n      name: '--project-root <string>',\n      description:\n        'Root path to your React Native project. When not specified, defaults to current working directory.',\n      default: process.cwd(),\n    },\n    {\n      name: '--verify-cache',\n      description:\n        'Whether to verify the cache. Currently only applies to npm cache.',\n      default: false,\n    },\n  ],\n};\n"], "mappings": ";;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA6B;AAoB7B,MAAMA,cAAc,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC;AAE5C,MAAMC,OAAO,GAAG,IAAAC,iBAAS,EAACC,QAAE,CAAC;AAC7B,MAAMC,cAAc,GAAG;EAACC,UAAU,EAAE,CAAC;EAAEC,SAAS,EAAE,IAAI;EAAEC,KAAK,EAAE;AAAI,CAAC;AAEpE,SAASC,kBAAkB,CAACC,SAAiB,EAAW;EACtD,OAAOA,SAAS,CAACC,QAAQ,CAAC,GAAG,CAAC,IAAID,SAAS,CAACC,QAAQ,CAAC,GAAG,CAAC;AAC3D;AAEO,eAAeC,QAAQ,CAACF,SAAiB,EAAiB;EAC/D,IAAI;IACF,IAAID,kBAAkB,CAACC,SAAS,CAAC,EAAE;MACjC,MAAMG,WAAW,GAAG,MAAMC,mBAAI,CAACC,KAAK,CAACL,SAAS,EAAE;QAACM,SAAS,EAAE;MAAK,CAAC,CAAC;MAEnE,KAAK,MAAMC,GAAG,IAAIJ,WAAW,EAAE;QAC7B,MAAMX,OAAO,CAACe,GAAG,EAAEZ,cAAc,CAAC;MACpC;IACF,CAAC,MAAM;MACL,IAAI,CAAC,IAAAa,gBAAU,EAACR,SAAS,CAAC,EAAE;QAC1B;MACF;MACA,MAAMR,OAAO,CAACQ,SAAS,EAAEL,cAAc,CAAC;IAC1C;EACF,CAAC,CAAC,OAAOc,KAAK,EAAE;IACdC,kBAAM,CAACD,KAAK,CAAE,mDAAkDA,KAAM,EAAC,CAAC;EAC1E;AACF;AAEA,eAAeE,eAAe,CAC5BC,MAAmB,EACY;EAC/B,MAAM;IAACC;EAAM,CAAC,GAAG,MAAM,IAAAC,kBAAM,EAAC;IAC5BC,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,4BAA4B;IACrCC,OAAO,EAAEC,MAAM,CAACC,OAAO,CAACR,MAAM,CAAC,CAACS,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,MAAM;MACrDC,KAAK,EAAG,GAAEF,GAAI,IAAGG,gBAAK,CAACC,GAAG,CAAE,IAAGH,KAAK,CAACI,WAAY,GAAE,CAAE,EAAC;MACtDC,KAAK,EAAEN,GAAG;MACVO,QAAQ,EAAEtC,cAAc,CAACuC,QAAQ,CAACR,GAAG;IACvC,CAAC,CAAC,CAAC;IACHS,GAAG,EAAE;EACP,CAAC,CAAC;EACF,OAAOlB,MAAM;AACf;AAEO,eAAemB,KAAK,CACzBC,KAAe,EACfC,GAAc,EACdC,YAAkB,EACH;EACf,MAAM;IAACC,OAAO;IAAEC,WAAW;IAAEC;EAAW,CAAC,GAAGH,YAAY;EACxD,IAAI,CAAC,IAAA3B,gBAAU,EAAC6B,WAAW,CAAC,EAAE;IAC5B,MAAM,IAAIE,KAAK,CAAE,0BAAyBF,WAAY,EAAC,CAAC;EAC1D;EAEA,MAAMG,QAAqB,GAAG;IAC5BC,OAAO,EAAE;MACPd,WAAW,EAAE,mCAAmC;MAChDe,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,oBAAoB;QAC3BC,MAAM,EAAE,YAAY;UAAA;UAClB,MAAMC,OAAO,GACXC,aAAE,CAACC,QAAQ,EAAE,KAAK,OAAO,GACrBC,eAAI,CAACC,IAAI,CACP,yBAAAf,GAAG,CAACgB,OAAO,CAACT,OAAO,yDAAnB,qBAAqBU,SAAS,KAAI,SAAS,EAC3C,aAAa,CACd,GACDH,eAAI,CAACC,IAAI,CACP,0BAAAf,GAAG,CAACgB,OAAO,CAACT,OAAO,0DAAnB,sBAAqBU,SAAS,KAAI,SAAS,EAC3C,SAAS,CACV;UAEP,IAAI,IAAA3C,gBAAU,EAACqC,OAAO,CAAC,EAAE;YACvB,MAAMO,MAAM,GAAGJ,eAAI,CAACK,QAAQ,CAACR,OAAO,CAAC;YACrC,MAAM,IAAAS,gBAAK,EACTR,aAAE,CAACC,QAAQ,EAAE,KAAK,OAAO,GAAGK,MAAM,GAAI,KAAIA,MAAO,EAAC,EAClD,CAAC,OAAO,CAAC,EACT;cAACG,GAAG,EAAEP,eAAI,CAACQ,OAAO,CAACX,OAAO;YAAC,CAAC,CAC7B;UACH;QACF;MACF,CAAC;IAEL,CAAC;IACD,IAAIC,aAAE,CAACC,QAAQ,EAAE,KAAK,QAAQ,GAC1B;MACEU,SAAS,EAAE;QACT9B,WAAW,EAAE,iBAAiB;QAC9Be,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,2BAA2B;UAClCC,MAAM,EAAE,YAAY;YAClB,MAAM,IAAAU,gBAAK,EAAC,KAAK,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE;cAC9CC,GAAG,EAAElB;YACP,CAAC,CAAC;UACJ;QACF,CAAC,EACD;UACEM,KAAK,EAAE,4BAA4B;UACnCC,MAAM,EAAE,MAAM1C,QAAQ,CAAC,UAAU;QACnC,CAAC,EACD;UACEyC,KAAK,EAAE,6BAA6B;UACpCC,MAAM,EAAE,MAAM1C,QAAQ,CAAC,cAAc;QACvC,CAAC;MAEL;IACF,CAAC,GACDwD,SAAS,CAAC;IACdC,KAAK,EAAE;MACLhC,WAAW,EAAE,yBAAyB;MACtCe,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,mBAAmB;QAC1BC,MAAM,EAAE,MAAM1C,QAAQ,CAAE,GAAE4C,aAAE,CAACc,MAAM,EAAG,UAAS;MACjD,CAAC,EACD;QACEjB,KAAK,EAAE,mBAAmB;QAC1BC,MAAM,EAAE,MAAM1C,QAAQ,CAAE,GAAE4C,aAAE,CAACc,MAAM,EAAG,cAAa;MACrD,CAAC,EACD;QACEjB,KAAK,EAAE,0BAA0B;QACjCC,MAAM,EAAE,MAAM1C,QAAQ,CAAE,GAAE4C,aAAE,CAACc,MAAM,EAAG,UAAS;MACjD,CAAC;IAEL,CAAC;IACDC,GAAG,EAAE;MACHlC,WAAW,EAAE,WAAW;MACxBe,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,iBAAiB;QACxBC,MAAM,EAAE,YAAY;UAClB,MAAM,IAAAU,gBAAK,EAAC,KAAK,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE;YAACC,GAAG,EAAElB;UAAW,CAAC,CAAC;QAC/D;MACF,CAAC;IAEL,CAAC;IACDyB,QAAQ,EAAE;MACRnC,WAAW,EAAE,oCAAoC;MACjDe,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,eAAe;QACtBC,MAAM,EAAE,YAAY;UAClB,MAAM,IAAAU,gBAAK,EACTR,aAAE,CAACC,QAAQ,EAAE,KAAK,OAAO,GAAG,QAAQ,GAAG,SAAS,EAChD,CAAC,UAAU,CAAC,EACZ;YAACQ,GAAG,EAAElB;UAAW,CAAC,CACnB;QACH;MACF,CAAC,EACD;QACEM,KAAK,EAAE,uBAAuB;QAC9BC,MAAM,EAAE,YAAY;UAClB,MAAM,IAAAU,gBAAK,EAAC,UAAU,EAAE,CAAC,eAAe,CAAC,EAAE;YAACC,GAAG,EAAElB;UAAW,CAAC,CAAC;QAChE;MACF,CAAC;IAEL,CAAC;IACD0B,IAAI,EAAE;MACJpC,WAAW,EAAE,YAAY;MACzBe,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,kBAAkB;QACzBC,MAAM,EAAE,YAAY;UAClB,MAAM,IAAAU,gBAAK,EAAC,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;YAACC,GAAG,EAAElB;UAAW,CAAC,CAAC;QAC7D;MACF,CAAC;IAEL,CAAC;IACD2B,GAAG,EAAE;MACHrC,WAAW,EACT,+EAA+E;MACjFe,KAAK,EAAE,CACL;QACEC,KAAK,EAAE,qBAAqB;QAC5BC,MAAM,EAAE,MAAM1C,QAAQ,CAAE,GAAEmC,WAAY,eAAc;MACtD,CAAC,EACD,IAAIC,WAAW,GACX,CACE;QACEK,KAAK,EAAE,kBAAkB;QACzBC,MAAM,EAAE,YAAY;UAClB,MAAM,IAAAU,gBAAK,EAAC,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE;YAACC,GAAG,EAAElB;UAAW,CAAC,CAAC;QAC7D;MACF,CAAC,CACF,GACD,EAAE,CAAC;IAEX;EACF,CAAC;EAED,MAAMzB,MAAM,GAAGwB,OAAO,GAAGA,OAAO,CAAC6B,KAAK,CAAC,GAAG,CAAC,GAAG,MAAMtD,eAAe,CAAC6B,QAAQ,CAAC;EAC7E,IAAI,CAAC5B,MAAM,IAAIA,MAAM,CAACsD,MAAM,KAAK,CAAC,EAAE;IAClC;EACF;EAEA,MAAMC,OAAO,GAAG,IAAAC,qBAAS,GAAE;EAC3B,KAAK,MAAM7C,KAAK,IAAIX,MAAM,EAAE;IAC1B,MAAMyD,QAAQ,GAAG7B,QAAQ,CAACjB,KAAK,CAAC;IAChC,IAAI,CAAC8C,QAAQ,EAAE;MACbF,OAAO,CAACG,IAAI,CAAE,kBAAiB/C,KAAM,EAAC,CAAC;MACvC;IACF;IAEA,KAAK,MAAM;MAACqB,MAAM;MAAED;IAAK,CAAC,IAAI0B,QAAQ,CAAC3B,KAAK,EAAE;MAC5CyB,OAAO,CAACI,KAAK,CAAC5B,KAAK,CAAC;MACpB,MAAMC,MAAM,EAAE,CACX4B,IAAI,CAAC,MAAM;QACVL,OAAO,CAACM,OAAO,EAAE;MACnB,CAAC,CAAC,CACDC,KAAK,CAAEC,CAAC,IAAK;QACZR,OAAO,CAACS,IAAI,CAAE,GAAEjC,KAAM,MAAKgC,CAAE,EAAC,CAAC;MACjC,CAAC,CAAC;IACN;EACF;AACF;AAAC,eAEc;EACbE,IAAI,EAAE7C,KAAK;EACXhB,IAAI,EAAE,OAAO;EACbW,WAAW,EACT,0EAA0E;EAC5EmD,OAAO,EAAE,CACP;IACE9D,IAAI,EAAE,oBAAoB;IAC1BW,WAAW,EACT;EACJ,CAAC,EACD;IACEX,IAAI,EAAE,yBAAyB;IAC/BW,WAAW,EACT,oGAAoG;IACtGoD,OAAO,EAAEC,OAAO,CAACzB,GAAG;EACtB,CAAC,EACD;IACEvC,IAAI,EAAE,gBAAgB;IACtBW,WAAW,EACT,mEAAmE;IACrEoD,OAAO,EAAE;EACX,CAAC;AAEL,CAAC;AAAA"}