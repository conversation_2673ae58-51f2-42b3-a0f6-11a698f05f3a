// 🏋️‍♂️ FitPersona REST API Server
// Express.js backend for React Native app

const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Helper function to call Python functions
function callPythonFunction(functionName, args) {
    return new Promise((resolve, reject) => {
        const pythonCode = `
from app import ${functionName}
import json
import sys

try:
    result = ${functionName}(${args.map(arg =>
        typeof arg === 'string' ? `"${arg}"` : arg
    ).join(', ')})
    print(json.dumps(result, ensure_ascii=False))
except Exception as e:
    print(json.dumps({"error": str(e)}, ensure_ascii=False))
`;

        const python = spawn('python', ['-c', pythonCode], {
            encoding: 'utf8',
            env: { ...process.env, PYTHONIOENCODING: 'utf-8' }
        });
        let output = '';
        let error = '';

        python.stdout.on('data', (data) => {
            output += data.toString();
        });

        python.stderr.on('data', (data) => {
            error += data.toString();
        });

        python.on('close', (code) => {
            if (code === 0) {
                try {
                    const result = JSON.parse(output.trim());
                    resolve(result);
                } catch (e) {
                    reject(new Error('Invalid JSON response'));
                }
            } else {
                reject(new Error(error || 'Python execution failed'));
            }
        });
    });
}

// 🏠 Welcome endpoint
app.get('/', (req, res) => {
    res.json({
        message: '🏋️‍♂️ FitPersona API - Kişiye Özel Spor ve BMI Asistanı',
        version: '1.0.0',
        endpoints: {
            'POST /api/calculate-bmi': 'BMI hesaplama',
            'POST /api/nutrition-plan': 'Beslenme önerileri',
            'POST /api/exercise-plan': 'Egzersiz planı',
            'POST /api/weekly-schedule': 'Haftalık program',
            'POST /api/complete-plan': 'Komple fitness planı'
        }
    });
});

// 📊 BMI Calculation
app.post('/api/calculate-bmi', async (req, res) => {
    try {
        const { height_cm, weight_kg } = req.body;

        if (!height_cm || !weight_kg) {
            return res.status(400).json({
                error: 'Boy (height_cm) ve kilo (weight_kg) gerekli'
            });
        }

        const result = await callPythonFunction('calculate_bmi', [height_cm, weight_kg]);
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 🍎 Nutrition Plan
app.post('/api/nutrition-plan', async (req, res) => {
    try {
        const { bmi, gender, age, activity_level = 'orta' } = req.body;

        if (!bmi || !gender || !age) {
            return res.status(400).json({
                error: 'BMI, cinsiyet (gender) ve yaş (age) gerekli'
            });
        }

        const result = await callPythonFunction('get_nutrition_advice',
            [bmi, gender, age, activity_level]);
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 🏋️‍♂️ Exercise Plan
app.post('/api/exercise-plan', async (req, res) => {
    try {
        const { bmi, gender, age, fitness_goal = 'auto' } = req.body;

        if (!bmi || !gender || !age) {
            return res.status(400).json({
                error: 'BMI, cinsiyet (gender) ve yaş (age) gerekli'
            });
        }

        const result = await callPythonFunction('get_exercise_plan',
            [bmi, gender, age, fitness_goal]);
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 📅 Weekly Schedule
app.post('/api/weekly-schedule', async (req, res) => {
    try {
        const { fitness_goal = 'form_koruma', intensity = 'orta' } = req.body;

        const result = await callPythonFunction('get_weekly_schedule',
            [fitness_goal, intensity]);
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 🎯 Complete Fitness Plan
app.post('/api/complete-plan', async (req, res) => {
    try {
        const {
            height_cm,
            weight_kg,
            gender,
            age,
            activity_level = 'orta',
            fitness_goal = 'auto'
        } = req.body;

        if (!height_cm || !weight_kg || !gender || !age) {
            return res.status(400).json({
                error: 'Boy, kilo, cinsiyet ve yaş gerekli'
            });
        }

        // BMI hesapla
        const bmiResult = await callPythonFunction('calculate_bmi', [height_cm, weight_kg]);
        if (bmiResult.error) {
            return res.status(400).json(bmiResult);
        }

        const bmi = bmiResult.bmi;

        // Beslenme planı
        const nutritionResult = await callPythonFunction('get_nutrition_advice',
            [bmi, gender, age, activity_level]);

        // Egzersiz planı
        const exerciseResult = await callPythonFunction('get_exercise_plan',
            [bmi, gender, age, fitness_goal]);

        // Haftalık program
        const weeklyResult = await callPythonFunction('get_weekly_schedule',
            [exerciseResult.fitness_goal || fitness_goal, exerciseResult.intensity_level || 'orta']);

        // Tüm sonuçları birleştir
        const completePlan = {
            user_profile: {
                height_cm,
                weight_kg,
                gender,
                age,
                activity_level
            },
            bmi_analysis: bmiResult,
            nutrition_plan: nutritionResult,
            exercise_plan: exerciseResult,
            weekly_schedule: weeklyResult,
            status: 'success'
        };

        res.json(completePlan);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// 🚀 Start server
app.listen(PORT, () => {
    console.log(`🏋️‍♂️ FitPersona API Server running on port ${PORT}`);
    console.log(`📱 Ready for React Native app connection!`);
    console.log(`🌐 API URL: http://localhost:${PORT}`);
});

module.exports = app;
