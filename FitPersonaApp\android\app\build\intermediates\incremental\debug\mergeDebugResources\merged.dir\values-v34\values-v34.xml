<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="m3_sys_color_dynamic_dark_background">@android:color/system_background_dark</color>
    <color name="m3_sys_color_dynamic_dark_error">@android:color/system_error_dark</color>
    <color name="m3_sys_color_dynamic_dark_error_container">@android:color/system_error_container_dark</color>
    <color name="m3_sys_color_dynamic_dark_inverse_on_surface">@android:color/system_on_surface_light</color>
    <color name="m3_sys_color_dynamic_dark_inverse_primary">@android:color/system_primary_light</color>
    <color name="m3_sys_color_dynamic_dark_inverse_surface">@android:color/system_surface_light</color>
    <color name="m3_sys_color_dynamic_dark_on_background">@android:color/system_on_background_dark</color>
    <color name="m3_sys_color_dynamic_dark_on_error">@android:color/system_on_error_dark</color>
    <color name="m3_sys_color_dynamic_dark_on_error_container">@android:color/system_on_error_container_dark</color>
    <color name="m3_sys_color_dynamic_dark_on_primary">@android:color/system_on_primary_dark</color>
    <color name="m3_sys_color_dynamic_dark_on_primary_container">@android:color/system_on_primary_container_dark</color>
    <color name="m3_sys_color_dynamic_dark_on_secondary">@android:color/system_on_secondary_dark</color>
    <color name="m3_sys_color_dynamic_dark_on_secondary_container">@android:color/system_on_secondary_container_dark</color>
    <color name="m3_sys_color_dynamic_dark_on_surface">@android:color/system_on_surface_dark</color>
    <color name="m3_sys_color_dynamic_dark_on_surface_variant">@android:color/system_on_surface_variant_dark</color>
    <color name="m3_sys_color_dynamic_dark_on_tertiary">@android:color/system_on_tertiary_dark</color>
    <color name="m3_sys_color_dynamic_dark_on_tertiary_container">@android:color/system_on_tertiary_container_dark</color>
    <color name="m3_sys_color_dynamic_dark_outline">@android:color/system_outline_dark</color>
    <color name="m3_sys_color_dynamic_dark_outline_variant">@android:color/system_outline_variant_dark</color>
    <color name="m3_sys_color_dynamic_dark_primary">@android:color/system_primary_dark</color>
    <color name="m3_sys_color_dynamic_dark_primary_container">@android:color/system_primary_container_dark</color>
    <color name="m3_sys_color_dynamic_dark_secondary">@android:color/system_secondary_dark</color>
    <color name="m3_sys_color_dynamic_dark_secondary_container">@android:color/system_secondary_container_dark</color>
    <color name="m3_sys_color_dynamic_dark_surface">@android:color/system_surface_dark</color>
    <color name="m3_sys_color_dynamic_dark_surface_bright">@android:color/system_surface_bright_dark</color>
    <color name="m3_sys_color_dynamic_dark_surface_container">@android:color/system_surface_container_dark</color>
    <color name="m3_sys_color_dynamic_dark_surface_container_high">@android:color/system_surface_container_high_dark</color>
    <color name="m3_sys_color_dynamic_dark_surface_container_highest">@android:color/system_surface_container_highest_dark</color>
    <color name="m3_sys_color_dynamic_dark_surface_container_low">@android:color/system_surface_container_low_dark</color>
    <color name="m3_sys_color_dynamic_dark_surface_container_lowest">@android:color/system_surface_container_lowest_dark</color>
    <color name="m3_sys_color_dynamic_dark_surface_dim">@android:color/system_surface_dim_dark</color>
    <color name="m3_sys_color_dynamic_dark_surface_variant">@android:color/system_surface_variant_dark</color>
    <color name="m3_sys_color_dynamic_dark_tertiary">@android:color/system_tertiary_dark</color>
    <color name="m3_sys_color_dynamic_dark_tertiary_container">@android:color/system_tertiary_container_dark</color>
    <color name="m3_sys_color_dynamic_light_background">@android:color/system_background_light</color>
    <color name="m3_sys_color_dynamic_light_error">@android:color/system_error_light</color>
    <color name="m3_sys_color_dynamic_light_error_container">@android:color/system_error_container_light</color>
    <color name="m3_sys_color_dynamic_light_inverse_on_surface">@android:color/system_on_surface_dark</color>
    <color name="m3_sys_color_dynamic_light_inverse_primary">@android:color/system_primary_dark</color>
    <color name="m3_sys_color_dynamic_light_inverse_surface">@android:color/system_surface_dark</color>
    <color name="m3_sys_color_dynamic_light_on_background">@android:color/system_on_background_light</color>
    <color name="m3_sys_color_dynamic_light_on_error">@android:color/system_on_error_light</color>
    <color name="m3_sys_color_dynamic_light_on_error_container">@android:color/system_on_error_container_light</color>
    <color name="m3_sys_color_dynamic_light_on_primary">@android:color/system_on_primary_light</color>
    <color name="m3_sys_color_dynamic_light_on_primary_container">@android:color/system_on_primary_container_light</color>
    <color name="m3_sys_color_dynamic_light_on_secondary">@android:color/system_on_secondary_light</color>
    <color name="m3_sys_color_dynamic_light_on_secondary_container">@android:color/system_on_secondary_container_light</color>
    <color name="m3_sys_color_dynamic_light_on_surface">@android:color/system_on_surface_light</color>
    <color name="m3_sys_color_dynamic_light_on_surface_variant">@android:color/system_on_surface_variant_light</color>
    <color name="m3_sys_color_dynamic_light_on_tertiary">@android:color/system_on_tertiary_light</color>
    <color name="m3_sys_color_dynamic_light_on_tertiary_container">@android:color/system_on_tertiary_container_light</color>
    <color name="m3_sys_color_dynamic_light_outline">@android:color/system_outline_light</color>
    <color name="m3_sys_color_dynamic_light_outline_variant">@android:color/system_outline_variant_light</color>
    <color name="m3_sys_color_dynamic_light_primary">@android:color/system_primary_light</color>
    <color name="m3_sys_color_dynamic_light_primary_container">@android:color/system_primary_container_light</color>
    <color name="m3_sys_color_dynamic_light_secondary">@android:color/system_secondary_light</color>
    <color name="m3_sys_color_dynamic_light_secondary_container">@android:color/system_secondary_container_light</color>
    <color name="m3_sys_color_dynamic_light_surface">@android:color/system_surface_light</color>
    <color name="m3_sys_color_dynamic_light_surface_bright">@android:color/system_surface_bright_light</color>
    <color name="m3_sys_color_dynamic_light_surface_container">@android:color/system_surface_container_light</color>
    <color name="m3_sys_color_dynamic_light_surface_container_high">@android:color/system_surface_container_high_light</color>
    <color name="m3_sys_color_dynamic_light_surface_container_highest">@android:color/system_surface_container_highest_light</color>
    <color name="m3_sys_color_dynamic_light_surface_container_low">@android:color/system_surface_container_low_light</color>
    <color name="m3_sys_color_dynamic_light_surface_container_lowest">@android:color/system_surface_container_lowest_light</color>
    <color name="m3_sys_color_dynamic_light_surface_dim">@android:color/system_surface_dim_light</color>
    <color name="m3_sys_color_dynamic_light_surface_variant">@android:color/system_surface_variant_light</color>
    <color name="m3_sys_color_dynamic_light_tertiary">@android:color/system_tertiary_light</color>
    <color name="m3_sys_color_dynamic_light_tertiary_container">@android:color/system_tertiary_container_light</color>
    <color name="m3_sys_color_dynamic_on_primary_fixed">@android:color/system_on_primary_fixed</color>
    <color name="m3_sys_color_dynamic_on_primary_fixed_variant">@android:color/system_on_primary_fixed_variant</color>
    <color name="m3_sys_color_dynamic_on_secondary_fixed">@android:color/system_on_secondary_fixed</color>
    <color name="m3_sys_color_dynamic_on_secondary_fixed_variant">@android:color/system_on_secondary_fixed_variant</color>
    <color name="m3_sys_color_dynamic_on_tertiary_fixed">@android:color/system_on_tertiary_fixed</color>
    <color name="m3_sys_color_dynamic_on_tertiary_fixed_variant">@android:color/system_on_tertiary_fixed_variant</color>
    <color name="m3_sys_color_dynamic_primary_fixed">@android:color/system_primary_fixed</color>
    <color name="m3_sys_color_dynamic_primary_fixed_dim">@android:color/system_primary_fixed_dim</color>
    <color name="m3_sys_color_dynamic_secondary_fixed">@android:color/system_secondary_fixed</color>
    <color name="m3_sys_color_dynamic_secondary_fixed_dim">@android:color/system_secondary_fixed_dim</color>
    <color name="m3_sys_color_dynamic_tertiary_fixed">@android:color/system_tertiary_fixed</color>
    <color name="m3_sys_color_dynamic_tertiary_fixed_dim">@android:color/system_tertiary_fixed_dim</color>
    <color name="material_dynamic_color_dark_error">@color/m3_sys_color_dynamic_dark_error</color>
    <color name="material_dynamic_color_dark_error_container">@color/m3_sys_color_dynamic_dark_error_container</color>
    <color name="material_dynamic_color_dark_on_error">@color/m3_sys_color_dynamic_dark_on_error</color>
    <color name="material_dynamic_color_dark_on_error_container">@color/m3_sys_color_dynamic_dark_on_error_container</color>
    <color name="material_dynamic_color_light_error">@color/m3_sys_color_dynamic_light_error</color>
    <color name="material_dynamic_color_light_error_container">@color/m3_sys_color_dynamic_light_error_container</color>
    <color name="material_dynamic_color_light_on_error">@color/m3_sys_color_dynamic_light_on_error</color>
    <color name="material_dynamic_color_light_on_error_container">@color/m3_sys_color_dynamic_light_on_error_container</color>
</resources>