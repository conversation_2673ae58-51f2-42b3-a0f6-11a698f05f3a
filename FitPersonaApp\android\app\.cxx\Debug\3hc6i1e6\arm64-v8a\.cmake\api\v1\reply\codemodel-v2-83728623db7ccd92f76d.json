{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-434d5ef7bb61966a2694.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/Weather-Forecast-MCP-main/FitPersonaApp/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-8d788a2c56555b7df3f9.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/Weather-Forecast-MCP-main/FitPersonaApp/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [4]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-71ea2c597a086138b76c.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/Weather-Forecast-MCP-main/FitPersonaApp/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [3]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-a2045bdcc1ded1f6ee75.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Desktop/Weather-Forecast-MCP-main/FitPersonaApp/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [1]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-aee01e13c089937e026b.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-ae1509e462a393e48571.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-4d48ba5dc2856ae9a8d3.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-bba45ba1b943a9b918fc.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-d3b4e8baf17319bce1ae.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/Weather-Forecast-MCP-main/FitPersonaApp/android/app/.cxx/Debug/3hc6i1e6/arm64-v8a", "source": "C:/Users/<USER>/Desktop/Weather-Forecast-MCP-main/FitPersonaApp/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}