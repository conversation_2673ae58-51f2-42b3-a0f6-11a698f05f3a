
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleJniCpp.js
 */

#include "safeareacontext.h"

namespace facebook::react {

static facebook::jsi::Value __hostFunction_NativeSafeAreaContextSpecJSI_getConstants(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, ObjectKind, "getConstants", "()Ljava/util/Map;", args, count, cachedMethodId);
}

NativeSafeAreaContextSpecJSI::NativeSafeAreaContextSpecJSI(const JavaTurboModule::InitParams &params)
  : JavaTurboModule(params) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeSafeAreaContextSpecJSI_getConstants};
}

std::shared_ptr<TurboModule> safeareacontext_ModuleProvider(const std::string &moduleName, const JavaTurboModule::InitParams &params) {
  if (moduleName == "RNCSafeAreaContext") {
    return std::make_shared<NativeSafeAreaContextSpecJSI>(params);
  }
  return nullptr;
}

} // namespace facebook::react
