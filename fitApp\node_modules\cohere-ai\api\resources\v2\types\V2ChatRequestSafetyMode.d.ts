/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * Used to select the [safety instruction](https://docs.cohere.com/v2/docs/safety-modes) inserted into the prompt. Defaults to `CONTEXTUAL`.
 * When `OFF` is specified, the safety instruction will be omitted.
 *
 * Safety modes are not yet configurable in combination with `tools`, `tool_results` and `documents` parameters.
 *
 * **Note**: This parameter is only compatible newer Cohere models, starting with [Command R 08-2024](https://docs.cohere.com/docs/command-r#august-2024-release) and [Command R+ 08-2024](https://docs.cohere.com/docs/command-r-plus#august-2024-release).
 *
 * **Note**: `command-r7b-12-2024` and newer models only support `"CONTEXTUAL"` and `"STRICT"` modes.
 */
export declare type V2ChatRequestSafetyMode = "CONTEXTUAL" | "STRICT" | "OFF";
export declare const V2ChatRequestSafetyMode: {
    readonly Contextual: "CONTEXTUAL";
    readonly Strict: "STRICT";
    readonly Off: "OFF";
};
