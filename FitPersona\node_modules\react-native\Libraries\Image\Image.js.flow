/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 */

import type {Image} from './ImageTypes.flow';

export type {
  ImageProgressEventDataIOS,
  ImagePropsIOS,
  ImagePropsAndroid,
  ImageSourcePropType,
  ImageLoadEventData,
  ImageErrorEventData,
  ImagePropsBase,
  ImageProps,
  ImageBackgroundProps,
} from './ImageProps';

export type {ImageResolvedAssetSource, ImageSize} from './ImageTypes.flow';

declare export default Image;
