/ Header Record For PersistentHashMapValueStorageP Oreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/ReactExtension.ktM Lreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/ReactPlugin.ktX Wreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/ReactRootProjectPlugin.ktS Rreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/TaskConfiguration.kt` _react-native-gradle-plugin/src/main/kotlin/com/facebook/react/internal/PrivateReactExtension.ktY Xreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/BundleHermesCTask.kts rreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/GenerateAutolinkingNewArchitecturesFileTask.ktd creact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/GenerateCodegenArtifactsTask.kta `react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/GenerateCodegenSchemaTask.kt_ ^react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/GeneratePackageListTask.ktd creact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/BuildCodegenCLITask.kt_ ^react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/CustomExecTask.kta `react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/PrepareBoostTask.kt` _react-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/PrepareGlogTask.kti hreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/PreparePrefabHeadersTask.kto nreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/tasks/internal/utils/PrefabPreprocessingEntry.kt\ [react-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/AgpConfiguratorUtils.kt[ Zreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/BackwardCompatUtils.ktW Vreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/DependencyUtils.ktQ Preact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/FileUtils.kt\ [react-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/JdkConfiguratorUtils.kt\ [react-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/NdkConfiguratorUtils.ktQ Preact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/PathUtils.ktT Sreact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/ProjectUtils.ktU Treact-native-gradle-plugin/src/main/kotlin/com/facebook/react/utils/PropertyUtils.kt