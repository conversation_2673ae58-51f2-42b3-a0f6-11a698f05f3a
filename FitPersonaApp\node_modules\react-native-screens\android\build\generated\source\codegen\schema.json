{"modules": {"RNSFullWindowOverlay": {"type": "Component", "components": {"RNSFullWindowOverlay": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "accessibilityContainerViewIsModal", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": true}}], "commands": []}}}, "RNSModalScreen": {"type": "Component", "components": {"RNSModalScreen": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [{"name": "onAppear", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onDisappear", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onDismissed", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "dismissCount", "optional": false, "typeAnnotation": {"type": "Int32TypeAnnotation"}}]}}}, {"name": "onNativeDismissCancelled", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "dismissCount", "optional": false, "typeAnnotation": {"type": "Int32TypeAnnotation"}}]}}}, {"name": "onWillAppear", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onWillDisappear", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onHeaderHeightChange", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "headerHeight", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}]}}}, {"name": "onTransitionProgress", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "progress", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "closing", "optional": false, "typeAnnotation": {"type": "Int32TypeAnnotation"}}, {"name": "goingForward", "optional": false, "typeAnnotation": {"type": "Int32TypeAnnotation"}}]}}}, {"name": "onGestureCancel", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onHeaderBackButtonClicked", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onSheetDetentChanged", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "index", "optional": false, "typeAnnotation": {"type": "Int32TypeAnnotation"}}, {"name": "isStable", "optional": false, "typeAnnotation": {"type": "BooleanTypeAnnotation"}}]}}}], "props": [{"name": "sheetAllowedDetents", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "sheetLargestUndimmedDetent", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": -1}}, {"name": "sheetGrabberVisible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "sheetCornerRadius", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": -1}}, {"name": "sheetExpandsWhenScrolledToEdge", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "sheetInitialDetent", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "sheetElevation", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 24}}, {"name": "customAnimationOnSwipe", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "fullScreenSwipeEnabled", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "fullScreenSwipeShadowEnabled", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": true}}, {"name": "homeIndicatorHidden", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "preventNativeDismiss", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "gestureEnabled", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": true}}, {"name": "statusBarColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "statusBarHidden", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "screenOrientation", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "statusBarAnimation", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "statusBarStyle", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "statusBarTranslucent", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "gestureResponseDistance", "optional": true, "typeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "start", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "end", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "top", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "bottom", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}]}}, {"name": "stackPresentation", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "push", "options": ["push", "modal", "transparentModal", "fullScreenModal", "formSheet", "pageSheet", "containedModal", "containedTransparentModal"]}}, {"name": "stackAnimation", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "default", "options": ["default", "flip", "simple_push", "none", "fade", "slide_from_right", "slide_from_left", "slide_from_bottom", "fade_from_bottom", "ios_from_right", "ios_from_left"]}}, {"name": "transitionDuration", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 500}}, {"name": "replaceAnimation", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "pop", "options": ["pop", "push"]}}, {"name": "swipeDirection", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "horizontal", "options": ["vertical", "horizontal"]}}, {"name": "hideKeyboardOnSwipe", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "activityState", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": -1}}, {"name": "navigationBarColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "navigationBarTranslucent", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "navigationBarHidden", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "nativeBackButtonDismissalEnabled", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}], "commands": []}}}, "NativeScreensModule": {"type": "NativeModule", "aliasMap": {}, "enumMap": {}, "spec": {"eventEmitters": [], "methods": []}, "moduleName": "RNSModule"}, "RNSScreenContainer": {"type": "Component", "components": {"RNSScreenContainer": {"extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [], "commands": []}}}, "RNSScreenContentWrapper": {"type": "Component", "components": {"RNSScreenContentWrapper": {"extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [], "commands": []}}}, "RNSScreenFooter": {"type": "Component", "components": {"RNSScreenFooter": {"extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [], "commands": []}}}, "RNSScreen": {"type": "Component", "components": {"RNSScreen": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [{"name": "onAppear", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onDisappear", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onDismissed", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "dismissCount", "optional": false, "typeAnnotation": {"type": "Int32TypeAnnotation"}}]}}}, {"name": "onNativeDismissCancelled", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "dismissCount", "optional": false, "typeAnnotation": {"type": "Int32TypeAnnotation"}}]}}}, {"name": "onWillAppear", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onWillDisappear", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onHeaderHeightChange", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "headerHeight", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}]}}}, {"name": "onTransitionProgress", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "progress", "optional": false, "typeAnnotation": {"type": "DoubleTypeAnnotation"}}, {"name": "closing", "optional": false, "typeAnnotation": {"type": "Int32TypeAnnotation"}}, {"name": "goingForward", "optional": false, "typeAnnotation": {"type": "Int32TypeAnnotation"}}]}}}, {"name": "onGestureCancel", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onHeaderBackButtonClicked", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onSheetDetentChanged", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "index", "optional": false, "typeAnnotation": {"type": "Int32TypeAnnotation"}}, {"name": "isStable", "optional": false, "typeAnnotation": {"type": "BooleanTypeAnnotation"}}]}}}], "props": [{"name": "sheetAllowedDetents", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "sheetLargestUndimmedDetent", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": -1}}, {"name": "sheetGrabberVisible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "sheetCornerRadius", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": -1}}, {"name": "sheetExpandsWhenScrolledToEdge", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "sheetInitialDetent", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "sheetElevation", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 24}}, {"name": "customAnimationOnSwipe", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "fullScreenSwipeEnabled", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "fullScreenSwipeShadowEnabled", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": true}}, {"name": "homeIndicatorHidden", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "preventNativeDismiss", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "gestureEnabled", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": true}}, {"name": "statusBarColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "statusBarHidden", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "screenOrientation", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "statusBarAnimation", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "statusBarStyle", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "statusBarTranslucent", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "gestureResponseDistance", "optional": true, "typeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "start", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "end", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "top", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "bottom", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}]}}, {"name": "stackPresentation", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "push", "options": ["push", "modal", "transparentModal", "fullScreenModal", "formSheet", "pageSheet", "containedModal", "containedTransparentModal"]}}, {"name": "stackAnimation", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "default", "options": ["default", "flip", "simple_push", "none", "fade", "slide_from_right", "slide_from_left", "slide_from_bottom", "fade_from_bottom", "ios_from_right", "ios_from_left"]}}, {"name": "transitionDuration", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 500}}, {"name": "replaceAnimation", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "pop", "options": ["pop", "push"]}}, {"name": "swipeDirection", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "horizontal", "options": ["vertical", "horizontal"]}}, {"name": "hideKeyboardOnSwipe", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "activityState", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": -1}}, {"name": "navigationBarColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "navigationBarTranslucent", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "navigationBarHidden", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "nativeBackButtonDismissalEnabled", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}], "commands": []}}}, "RNSScreenNavigationContainer": {"type": "Component", "components": {"RNSScreenNavigationContainer": {"extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [], "commands": []}}}, "RNSScreenStackHeaderConfig": {"type": "Component", "components": {"RNSScreenStackHeaderConfig": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [{"name": "onAttached", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onDetached", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}], "props": [{"name": "backgroundColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "backTitle", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "backTitleFontFamily", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "backTitleFontSize", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "backTitleVisible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": true}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "direction", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "ltr", "options": ["rtl", "ltr"]}}, {"name": "hidden", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "hideShadow", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "largeTitle", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "largeTitleFontFamily", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "largeTitleFontSize", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "largeTitleFontWeight", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "largeTitleBackgroundColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "largeTitleHideShadow", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "largeTitleColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "translucent", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "title", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "titleFontFamily", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "titleFontSize", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "titleFontWeight", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "titleColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "disableBackButtonMenu", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "backButtonDisplayMode", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "default", "options": ["minimal", "default", "generic"]}}, {"name": "hideBackButton", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "backButtonInCustomView", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "blurEffect", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "none", "options": ["none", "extraLight", "light", "dark", "regular", "prominent", "systemUltraThinMaterial", "systemThinMaterial", "systemMaterial", "systemThickMaterial", "systemChromeMaterial", "systemUltraThinMaterialLight", "systemThinMaterialLight", "systemMaterialLight", "systemThickMaterialLight", "systemChromeMaterialLight", "systemUltraThinMaterialDark", "systemThinMaterialDark", "systemMaterialDark", "systemThickMaterialDark", "systemChromeMaterialDark"]}}, {"name": "topInsetEnabled", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}], "commands": []}}}, "RNSScreenStackHeaderSubview": {"type": "Component", "components": {"RNSScreenStackHeaderSubview": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "type", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "left", "options": ["back", "right", "left", "title", "center", "searchBar"]}}], "commands": []}}}, "RNSScreenStack": {"type": "Component", "components": {"RNSScreenStack": {"extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [{"name": "onFinishTransitioning", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}], "props": [], "commands": []}}}, "RNSSearchBar": {"type": "Component", "components": {"RNSSearchBar": {"extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [{"name": "onSearchFocus", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onSearchBlur", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onSearchButtonPress", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "text", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation"}}]}}}, {"name": "onCancelButtonPress", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onChangeText", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "text", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation"}}]}}}, {"name": "onClose", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}, {"name": "onOpen", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": []}}}], "props": [{"name": "hideWhenScrolling", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "autoCapitalize", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "none", "options": ["none", "words", "sentences", "characters"]}}, {"name": "placeholder", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "placement", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "stacked", "options": ["automatic", "inline", "stacked"]}}, {"name": "obscureBackground", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "hideNavigationBar", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "cancelButtonText", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "barTintColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "tintColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "textColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "disableBackButtonOverride", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "inputType", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "hintTextColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "headerIconColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "shouldShowHintSearchIcon", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": true}}], "commands": [{"name": "blur", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "params": [], "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}}}, {"name": "focus", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "params": [], "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}}}, {"name": "clearText", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "params": [], "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}}}, {"name": "toggleCancelButton", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "params": [{"name": "flag", "optional": false, "typeAnnotation": {"type": "BooleanTypeAnnotation"}}], "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}}}, {"name": "setText", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "params": [{"name": "text", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}], "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}}}, {"name": "cancelSearch", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "params": [], "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}}}]}}}}}